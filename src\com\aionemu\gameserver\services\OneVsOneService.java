package com.aionemu.gameserver.services;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledFuture;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.aionemu.gameserver.configs.main.CustomConfig;
import com.aionemu.gameserver.model.ChatType;
import com.aionemu.gameserver.model.Race;
import com.aionemu.gameserver.model.gameobjects.Npc;
import com.aionemu.gameserver.model.gameobjects.player.Player;
import com.aionemu.gameserver.model.onevsone.OneVsOneParticipant;
import com.aionemu.gameserver.model.onevsone.OneVsOneMatch;
import com.aionemu.gameserver.model.templates.spawns.SpawnTemplate;

import com.aionemu.gameserver.services.instance.InstanceService;
import com.aionemu.gameserver.services.mail.SystemMailService;
import com.aionemu.gameserver.services.teleport.TeleportService;
import com.aionemu.gameserver.services.player.PlayerReviveService;
import com.aionemu.gameserver.spawnengine.SpawnEngine;
import com.aionemu.gameserver.utils.PacketSendUtility;
import com.aionemu.gameserver.utils.ThreadPoolManager;
import com.aionemu.gameserver.world.World;
import com.aionemu.gameserver.world.WorldMapInstance;
import com.aionemu.gameserver.model.animations.TeleportAnimation;
import com.aionemu.gameserver.model.gameobjects.LetterType;

/**
 * OneVsOne PvP Service
 *
 * Features:
 * - Portal-based queue system for 1v1 PvP matches
 * - Automatic player matching based on level
 * - Cross-faction PvP support
 * - Reward distribution via express mail
 * - Admin commands for management
 * - Timed queue announcements
 * - Player disconnect timeout handling
 *
 * <AUTHOR> System
 */
public class OneVsOneService {

    private static final Logger log = LoggerFactory.getLogger(OneVsOneService.class);
    private static OneVsOneService instance;

    // System state
    private boolean systemActive = false;

    // Player management
    private final Map<Integer, OneVsOneParticipant> queuedPlayers = new ConcurrentHashMap<>();
    private final Map<Integer, OneVsOneParticipant> activePlayers = new ConcurrentHashMap<>();

    // Match management
    private final Map<Integer, OneVsOneMatch> activeMatches = new ConcurrentHashMap<>(); // instanceId -> match
    private final Map<Integer, Integer> playerToMatch = new ConcurrentHashMap<>(); // playerId -> instanceId

    // Entry limit tracking (playerId -> list of entry timestamps)
    private final Map<Integer, List<Long>> playerEntryHistory = new ConcurrentHashMap<>();

    // Death state tracking (playerId -> death timestamp)
    private final Map<Integer, Long> playerDeathTimes = new ConcurrentHashMap<>();
    
    // Offline state tracking (playerId -> offline timestamp)
    private final Map<Integer, Long> playerOfflineTimes = new ConcurrentHashMap<>();

    // Portal management
    private final List<Npc> portalNpcs = new ArrayList<>();

    // Map management
    private final Set<Integer> availableMaps = new HashSet<>();

    // Scheduled tasks
    private ScheduledFuture<?> queueProcessorTask;
    private ScheduledFuture<?> queueCleanupTask;
    private ScheduledFuture<?> deathStateMonitorTask;
    private ScheduledFuture<?> timedAnnouncementTask;
    private ScheduledFuture<?> offlineMonitorTask;
	

    // Spawn coordinates for each map (loaded from configuration)
    private final Map<Integer, List<float[]>> spawnCoordinates = new HashMap<>();

    private OneVsOneService() {
        initializeMaps();
        loadSpawnCoordinates();
    }

    public static OneVsOneService getInstance() {
        if (instance == null) {
            instance = new OneVsOneService();
        }
        return instance;
    }

    /**
     * Initialize available maps from configuration
     */
    private void initializeMaps() {
        availableMaps.clear();
        String[] mapIds = CustomConfig.ONEVSONE_MAPS.split(",");
        for (String mapId : mapIds) {
            try {
                availableMaps.add(Integer.parseInt(mapId.trim()));
            } catch (NumberFormatException e) {
                log.warn("Invalid map ID in OneVsOne configuration: {}", mapId);
            }
        }
        log.info("OneVsOne initialized with {} available maps", availableMaps.size());
    }

    /**
     * Load spawn coordinates from configuration
     */
    private void loadSpawnCoordinates() {
        spawnCoordinates.clear();

        // Load coordinates for each configured map
        for (Integer mapId : availableMaps) {
            String coordString = getSpawnCoordinatesForMap(mapId);
            if (coordString != null) {
                loadMapCoordinates(mapId, coordString);
            } else {
                log.warn("No spawn coordinates configured for map {}", mapId);
            }
        }

        log.info("OneVsOne spawn coordinates loaded for {} maps", spawnCoordinates.size());
    }

    /**
     * Get spawn coordinates string for a specific map ID
     */
    private String getSpawnCoordinatesForMap(int mapId) {
        switch (mapId) {
            case 320090000:
                return CustomConfig.ONEVSONE_SPAWN_320090000;
            default:
                log.warn("No spawn coordinates configuration found for map {}", mapId);
                return null;
        }
    }

    /**
     * Parse and load coordinates for a specific map
     */
    private void loadMapCoordinates(int mapId, String coordString) {
        if (coordString == null || coordString.trim().isEmpty()) {
            log.warn("No spawn coordinates configured for map {}", mapId);
            return;
        }

        List<float[]> coords = new ArrayList<>();
        String[] coordPairs = coordString.split(";");

        for (String coordPair : coordPairs) {
            try {
                String[] values = coordPair.trim().split(",");
                if (values.length >= 4) {
                    float x = Float.parseFloat(values[0].trim());
                    float y = Float.parseFloat(values[1].trim());
                    float z = Float.parseFloat(values[2].trim());
                    float h = Float.parseFloat(values[3].trim());
                    coords.add(new float[]{x, y, z, h});
                } else {
                    log.warn("Invalid coordinate format for map {}: {}", mapId, coordPair);
                }
            } catch (NumberFormatException e) {
                log.warn("Failed to parse coordinates for map {}: {}", mapId, coordPair, e);
            }
        }

        if (!coords.isEmpty()) {
            spawnCoordinates.put(mapId, coords);
            log.debug("Loaded {} spawn coordinates for map {}", coords.size(), mapId);
        } else {
            log.warn("No valid spawn coordinates found for map {}", mapId);
        }
    }

    /**
     * Initialize the OneVsOne service (called during server startup)
     */
    public void init() {
        if (!CustomConfig.ONEVSONE_ENABLED) {
            log.info("OneVsOne system is disabled in configuration");
            return;
        }

        log.info("Initializing OneVsOne service...");

        // Schedule automatic start if configured
        if (CustomConfig.ONEVSONE_AUTO_START) {
            if (!CustomConfig.ONEVSONE_SCHEDULE.isEmpty()) {
                // Schedule using cron expression
                com.aionemu.commons.services.CronService.getInstance().schedule(() -> {
                    if (!systemActive) {
                        log.info("Auto-starting OneVsOne system via schedule");
                        startSystem();
                    }
                }, CustomConfig.ONEVSONE_SCHEDULE);
                log.info("OneVsOne system scheduled with cron: {}", CustomConfig.ONEVSONE_SCHEDULE);
            } else {
                // Start immediately if auto-start is enabled but no schedule
                log.info("Auto-starting OneVsOne system immediately");
                startSystem();
            }
        } else {
            log.info("OneVsOne system requires manual start via admin command //1v1 start");
        }

        log.info("OneVsOne service initialized successfully!");
    }
    
    /**
     * Start the OneVsOne system
     */
    public void startSystem() {
        if (!CustomConfig.ONEVSONE_ENABLED) {
            log.info("OneVsOne system is disabled in configuration");
            return;
        }
        
        if (systemActive) {
            log.warn("OneVsOne system is already active");
            return;
        }
        
        log.info("Starting OneVsOne system");
        systemActive = true;
        
        // Spawn portals
        spawnPortals();
        
        // Start queue processor (check for matches every 5 seconds)
        queueProcessorTask = ThreadPoolManager.getInstance().scheduleAtFixedRate(() -> {
            processQueue();
        }, 5000, 5000);
        
        // Start queue cleanup (remove timed out players every minute)
        queueCleanupTask = ThreadPoolManager.getInstance().scheduleAtFixedRate(() -> {
            cleanupQueue();
        }, 60000, 60000);

        // Start death state monitor (check for stuck players every 1 second for immediate revival)
        deathStateMonitorTask = ThreadPoolManager.getInstance().scheduleAtFixedRate(() -> {
            monitorDeathStates();
        }, 1000, 1000);
        
        // Start offline monitor (check for disconnected players every 30 seconds)
        offlineMonitorTask = ThreadPoolManager.getInstance().scheduleAtFixedRate(() -> {
            checkOfflinePlayers();
        }, 30000, 30000);
        
        // Start timed announcements (every 5 minutes)
        timedAnnouncementTask = ThreadPoolManager.getInstance().scheduleAtFixedRate(() -> {
            announceQueueStatus();
        }, 300000, 300000); // 5 minutes
        
        // Announce system start
        if (CustomConfig.ONEVSONE_ANNOUNCEMENTS) {
            World.getInstance().forEachPlayer(player -> {
                PacketSendUtility.sendMessage(player, 
                    "OneVsOne PvP system is now active! Use portal 207011 to join the queue for 1v1 matches!", 
                    ChatType.BRIGHT_YELLOW_CENTER);
            });
        }
        
        log.info("OneVsOne system started successfully");
    }
    
    /**
     * Announce queue status to the world
     */
    private void announceQueueStatus() {
        if (!systemActive || !CustomConfig.ONEVSONE_ANNOUNCEMENTS) {
            return;
        }
        
        int queueSize = queuedPlayers.size();
        if (queueSize > 0) {
            String message = String.format("OneVsOne Queue: %d player%s waiting. Use portal 207011 to join!",
                queueSize, queueSize == 1 ? "" : "s");
            
            World.getInstance().forEachPlayer(player -> {
                PacketSendUtility.sendMessage(player, message, ChatType.BRIGHT_YELLOW_CENTER);
            });
            
            log.info("OneVsOne queue status announced: {} players in queue", queueSize);
        }
    }
    
    /**
     * Check for players who have been offline too long
     */
    private void checkOfflinePlayers() {
        if (!systemActive) {
            return;
        }
        
        long currentTime = System.currentTimeMillis();
        long offlineTimeout = 2 * 60 * 1000; // 2 minutes in milliseconds
        
        // Create a copy to avoid concurrent modification
        Map<Integer, Long> offlineCopy = new HashMap<>(playerOfflineTimes);
        
        for (Map.Entry<Integer, Long> entry : offlineCopy.entrySet()) {
            int playerId = entry.getKey();
            long offlineTime = entry.getValue();
            long offlineDuration = currentTime - offlineTime;
            
            if (offlineDuration >= offlineTimeout) {
                log.info("Player {} has been offline for {}ms, processing timeout", playerId, offlineDuration);
                handleOfflineTimeout(playerId);
            }
        }
    }
    
    /**
     * Handle offline timeout for a player
     */
    private void handleOfflineTimeout(int playerId) {
        // Remove from tracking
        playerOfflineTimes.remove(playerId);
        
        // Check if player is in a match
        Integer matchId = playerToMatch.get(playerId);
        if (matchId == null) {
            log.info("Player {} not in any match, skipping timeout handling", playerId);
            return;
        }
        
        OneVsOneMatch match = activeMatches.get(matchId);
        if (match == null) {
            log.info("Match {} not found for player {}", matchId, playerId);
            return;
        }
        
        OneVsOneParticipant offlineParticipant = match.getParticipant(playerId);
        if (offlineParticipant == null) {
            log.info("Participant not found in match {} for player {}", matchId, playerId);
            return;
        }
        
        OneVsOneParticipant onlineParticipant = match.getOpponent(playerId);
        if (onlineParticipant == null) {
            log.info("No opponent found for player {} in match {}", playerId, matchId);
            cleanupMatchWithoutWinner(match);
            return;
        }
        
        Player onlinePlayer = World.getInstance().getPlayer(onlineParticipant.getPlayerId());
        if (onlinePlayer != null && onlinePlayer.isOnline()) {
            // Award win to online player
            log.info("Awarding win to online player {} due to {} timeout", 
                onlineParticipant.getPlayerName(), offlineParticipant.getPlayerName());
            
            // Update statistics
            onlineParticipant.addWin();
            offlineParticipant.addLoss();
            
            // Send final messages
            PacketSendUtility.sendMessage(onlinePlayer,
                String.format("Your opponent %s was offline too long. You win by default!",
                    offlineParticipant.getPlayerName()),
                ChatType.BRIGHT_YELLOW_CENTER);
            
            // Teleport online player back to portal
            unsetOneVsOneParticipantState(onlinePlayer);
            teleportPlayerToPortalLocation(onlinePlayer);
            
            // Send rewards
            sendRewards(onlineParticipant, true);
            sendRewards(offlineParticipant, false);
            
            // Announce if enabled
            if (CustomConfig.ONEVSONE_ANNOUNCEMENTS) {
                String announcement = String.format("OneVsOne: %s wins by default (opponent offline)",
                    onlineParticipant.getPlayerName());
                World.getInstance().forEachPlayer(player -> {
                    PacketSendUtility.sendMessage(player, announcement, ChatType.BRIGHT_YELLOW_CENTER);
                });
            }
        } else {
            // Both players offline
            log.info("Both players offline in match {}, cleaning up", matchId);
            cleanupMatchWithoutWinner(match);
        }
        
        // Clean up match data
        cleanupMatchDataOnly(match);
    }
    
    /**
     * Handle player login (clear offline tracking)
     */
    public void onPlayerLogin(Player player) {
        playerOfflineTimes.remove(player.getObjectId());
    }
    
    /**
     * Stop the OneVsOne system
     */
    public void stopSystem() {
        if (!systemActive) {
            log.warn("OneVsOne system is not active");
            return;
        }
        
        log.info("Stopping OneVsOne system");
        systemActive = false;
        
        // Cancel scheduled tasks
        if (queueProcessorTask != null) {
            queueProcessorTask.cancel(false);
            queueProcessorTask = null;
        }
        
        if (queueCleanupTask != null) {
            queueCleanupTask.cancel(false);
            queueCleanupTask = null;
        }

        if (deathStateMonitorTask != null) {
            deathStateMonitorTask.cancel(false);
            deathStateMonitorTask = null;
        }
        
        if (timedAnnouncementTask != null) {
            timedAnnouncementTask.cancel(false);
            timedAnnouncementTask = null;
        }
        
        if (offlineMonitorTask != null) {
            offlineMonitorTask.cancel(false);
            offlineMonitorTask = null;
        }
        
        // Remove all players from queue and active matches
        removeAllPlayersFromQueue();
        endAllActiveMatches();
        
        // Despawn portals
        despawnPortals();
        
        // Clear offline tracking
        playerOfflineTimes.clear();
        
        // Announce system stop
        if (CustomConfig.ONEVSONE_ANNOUNCEMENTS) {
            World.getInstance().forEachPlayer(player -> {
                PacketSendUtility.sendMessage(player, 
                    "OneVsOne PvP system has been stopped.", 
                    ChatType.BRIGHT_YELLOW_CENTER);
            });
        }
        
        log.info("OneVsOne system stopped successfully");
    }
    
    /**
     * Spawn OneVsOne portals in configured locations
     */
    private void spawnPortals() {
        String[] locations = CustomConfig.ONEVSONE_PORTAL_LOCATIONS.split(";");

        for (String location : locations) {
            try {
                String[] parts = location.split(":");
                int worldId = Integer.parseInt(parts[0]);
                String[] coords = parts[1].split(",");
                float x = Float.parseFloat(coords[0]);
                float y = Float.parseFloat(coords[1]);
                float z = Float.parseFloat(coords[2]);
                byte h = Byte.parseByte(coords[3]);

                log.info("DEBUG: Spawning OneVsOne portal with NPC ID: {} at world {} coordinates {}, {}, {}",
                    CustomConfig.ONEVSONE_PORTAL_NPC, worldId, x, y, z);

                SpawnTemplate spawn = SpawnEngine.newSingleTimeSpawn(worldId, CustomConfig.ONEVSONE_PORTAL_NPC,
                    x, y, z, h, 0, "onevsone_portal");
                Npc portalNpc = (Npc) SpawnEngine.spawnObject(spawn, 0);

                if (portalNpc != null) {
                    portalNpcs.add(portalNpc);
                    log.info("DEBUG: Successfully spawned OneVsOne portal - NPC ID: {}, Object ID: {}, Name: {}",
                        portalNpc.getNpcId(), portalNpc.getObjectId(), portalNpc.getName());
                } else {
                    log.error("DEBUG: Failed to spawn OneVsOne portal - SpawnEngine returned null");
                }
            } catch (Exception e) {
                log.error("Failed to spawn OneVsOne portal at location: {}", location, e);
            }
        }

        log.info("Spawned {} OneVsOne portals", portalNpcs.size());
    }


    
    /**
     * Despawn all OneVsOne portals
     */
    private void despawnPortals() {
        for (Npc portal : portalNpcs) {
            if (portal != null && portal.isSpawned()) {
                portal.getController().delete();
            }
        }
        portalNpcs.clear();
        log.info("Despawned all OneVsOne portals");
    }
    
    /**
     * Check if player can join the queue
     */
    public boolean canJoinQueue(Player player) {
        if (!CustomConfig.ONEVSONE_ENABLED || !systemActive) {
            return false;
        }

        if (player.getLevel() < CustomConfig.ONEVSONE_MIN_LEVEL) {
            return false;
        }

        // Check if player is already in queue or in a match
        if (queuedPlayers.containsKey(player.getObjectId()) ||
            activePlayers.containsKey(player.getObjectId())) {
            return false;
        }

        // Check entry limit (2 entries per hour)
        if (!checkEntryLimit(player.getObjectId())) {
            return false;
        }

        return true;
    }
    
    /**
     * Add player to the queue
     */
    public boolean joinQueue(Player player) {
        if (!canJoinQueue(player)) {
            return false;
        }
        
        OneVsOneParticipant participant = new OneVsOneParticipant(player);
        queuedPlayers.put(player.getObjectId(), participant);
        
        PacketSendUtility.sendMessage(player, 
            "You have joined the OneVsOne queue! Looking for an opponent...", 
            ChatType.BRIGHT_YELLOW_CENTER);
        
        log.debug("Player {} joined OneVsOne queue", player.getName());
        
        // Try immediate matching
        processQueue();
        
        return true;
    }

    /**
     * Remove player from queue
     */
    public boolean leaveQueue(Player player) {
        OneVsOneParticipant participant = queuedPlayers.remove(player.getObjectId());
        if (participant != null) {
            PacketSendUtility.sendMessage(player,
                "You have left the OneVsOne queue.",
                ChatType.BRIGHT_YELLOW_CENTER);
            log.debug("Player {} left OneVsOne queue", player.getName());
            return true;
        }
        return false;
    }

    /**
     * Process the queue to find matches
     */
    private void processQueue() {
        if (queuedPlayers.size() < 2) {
            return;
        }

        log.info("DEBUG: Processing OneVsOne queue with {} players", queuedPlayers.size());
        List<OneVsOneParticipant> participants = new ArrayList<>(queuedPlayers.values());
        Set<Integer> matchedPlayerIds = new HashSet<>();

        // Try to match players - create multiple parallel matches
        for (int i = 0; i < participants.size() - 1; i++) {
            OneVsOneParticipant player1 = participants.get(i);

            // Skip if this player is already matched in this processing cycle
            if (matchedPlayerIds.contains(player1.getPlayerId())) {
                continue;
            }

            for (int j = i + 1; j < participants.size(); j++) {
                OneVsOneParticipant player2 = participants.get(j);

                // Skip if this player is already matched in this processing cycle
                if (matchedPlayerIds.contains(player2.getPlayerId())) {
                    continue;
                }

                log.info("DEBUG: Checking match compatibility between {} (level {}) and {} (level {})",
                    player1.getPlayerName(), player1.getPlayerLevel(),
                    player2.getPlayerName(), player2.getPlayerLevel());

                if (player1.canMatchWith(player2)) {
                    log.info("DEBUG: Players can match based on level difference");

                    // Check cross-faction rules
                    Player p1 = World.getInstance().getPlayer(player1.getPlayerId());
                    Player p2 = World.getInstance().getPlayer(player2.getPlayerId());

                    if (p1 != null && p2 != null) {
                        log.info("DEBUG: Both players online - {} ({}), {} ({})",
                            p1.getName(), p1.getRace(), p2.getName(), p2.getRace());
                        log.info("DEBUG: Cross-faction enabled: {}", CustomConfig.ONEVSONE_CROSS_FACTION);

                        if (!CustomConfig.ONEVSONE_CROSS_FACTION && p1.getRace() != p2.getRace()) {
                            log.info("DEBUG: Skipping match - cross-faction disabled and races differ");
                            continue; // Skip if cross-faction is disabled and races differ
                        }

                        log.info("DEBUG: Creating match between {} and {}", p1.getName(), p2.getName());
                        // Create match
                        createMatch(player1, player2);

                        // Mark both players as matched in this processing cycle
                        matchedPlayerIds.add(player1.getPlayerId());
                        matchedPlayerIds.add(player2.getPlayerId());

                        // Break inner loop to find next available player1
                        break;
                    } else {
                        log.warn("DEBUG: One or both players not online - p1: {}, p2: {}",
                            p1 != null ? p1.getName() : "null", p2 != null ? p2.getName() : "null");
                    }
                } else {
                    log.info("DEBUG: Players cannot match - level difference too large ({} vs {})",
                        player1.getPlayerLevel(), player2.getPlayerLevel());
                }
            }
        }

        if (matchedPlayerIds.isEmpty()) {
            log.info("DEBUG: No matches created in this queue processing cycle");
        }
    }

    /**
     * Create a match between two participants - Round-based system
     */
    private void createMatch(OneVsOneParticipant participant1, OneVsOneParticipant participant2) {
        log.info("DEBUG: Creating match between {} and {}", participant1.getPlayerName(), participant2.getPlayerName());

        // Remove from queue (entry limits already checked in canJoinQueue)
        queuedPlayers.remove(participant1.getPlayerId());
        queuedPlayers.remove(participant2.getPlayerId());

        // Select random map
        int mapId = selectRandomMap();
        log.info("DEBUG: Selected map ID: {}", mapId);
        if (mapId == 0) {
            log.error("No available maps for OneVsOne match");
            // Return players to queue
            queuedPlayers.put(participant1.getPlayerId(), participant1);
            queuedPlayers.put(participant2.getPlayerId(), participant2);
            return;
        }

        // Create instance
        log.info("DEBUG: Creating instance for map {}", mapId);
        WorldMapInstance instance = InstanceService.getNextAvailableInstance(mapId, 0, (byte) 0, 2, true);
        if (instance == null) {
            log.error("Failed to create instance for OneVsOne match on map {}", mapId);
            // Return players to queue
            queuedPlayers.put(participant1.getPlayerId(), participant1);
            queuedPlayers.put(participant2.getPlayerId(), participant2);
            return;
        }
        log.info("DEBUG: Successfully created instance {} for map {}", instance.getInstanceId(), mapId);

        // Register players with instance
        instance.register(participant1.getPlayerId());
        instance.register(participant2.getPlayerId());

        // Get spawn coordinates
        float[][] spawnCoords = getTwoRandomSpawnCoordinates(mapId);

        // Create match object
        OneVsOneMatch match = new OneVsOneMatch(participant1, participant2, mapId,
                                              instance.getInstanceId(), instance,
                                              spawnCoords[0], spawnCoords[1]);

        // Add to tracking maps
        activeMatches.put(instance.getInstanceId(), match);
        playerToMatch.put(participant1.getPlayerId(), instance.getInstanceId());
        playerToMatch.put(participant2.getPlayerId(), instance.getInstanceId());

        // Record entry times
        recordEntry(participant1.getPlayerId());
        recordEntry(participant2.getPlayerId());

        // Start match
        participant1.startMatch(participant2, mapId, instance.getInstanceId());
        participant2.startMatch(participant1, mapId, instance.getInstanceId());

        // Add to active players
        activePlayers.put(participant1.getPlayerId(), participant1);
        activePlayers.put(participant2.getPlayerId(), participant2);

        // Clear monsters and start match
        ThreadPoolManager.getInstance().schedule(() -> {
            clearInstanceMonsters(instance);
            startMatch(match);
        }, 1000); // 1 second delay

        log.info("OneVsOne match created: {} vs {} on map {} (Round-based)",
            participant1.getPlayerName(), participant2.getPlayerName(), mapId);
    }

    /**
     * Select a random map for the match
     */
    private int selectRandomMap() {
        if (availableMaps.isEmpty()) {
            return 0;
        }

        List<Integer> mapList = new ArrayList<>(availableMaps);
        return mapList.get(new Random().nextInt(mapList.size()));
    }

    /**
     * Get two different random spawn coordinates for the specified map
     * Returns array of two coordinate arrays: [player1Coords, player2Coords]
     * Each coordinate array contains [x, y, z, heading]
     */
    private float[][] getTwoRandomSpawnCoordinates(int mapId) {
        List<float[]> availableSpawns = spawnCoordinates.get(mapId);
        if (availableSpawns == null || availableSpawns.isEmpty()) {
            log.warn("No spawn coordinates found for map {}, using fallback", mapId);
            return new float[][]{
                {500.0f, 500.0f, 500.0f, 0},
                {520.0f, 520.0f, 500.0f, 0}
            };
        }

        if (availableSpawns.size() < 2) {
            log.warn("Only {} spawn points available for map {}, players may spawn close together",
                availableSpawns.size(), mapId);
            float[] spawn1 = availableSpawns.get(0);
            float[] spawn2 = availableSpawns.size() > 1 ? availableSpawns.get(1) : spawn1;
            return new float[][]{spawn1, spawn2};
        }

        // Select two different random spawn points
        Random random = new Random();
        int index1 = random.nextInt(availableSpawns.size());
        int index2;
        do {
            index2 = random.nextInt(availableSpawns.size());
        } while (index2 == index1);

        return new float[][]{availableSpawns.get(index1), availableSpawns.get(index2)};
    }

    /**
     * Teleport both players to match instance with different spawn points
     */
    private void teleportPlayersToMatch(Player player1, Player player2, WorldMapInstance instance) {
        // Get two different random spawn coordinates
        float[][] spawnCoords = getTwoRandomSpawnCoordinates(instance.getMapId());

        // Teleport player 1
        float[] coords1 = spawnCoords[0];
        TeleportService.teleportTo(player1, instance.getMapId(), instance.getInstanceId(),
            coords1[0], coords1[1], coords1[2], (byte) coords1[3], TeleportAnimation.FADE_OUT_BEAM);

        // Teleport player 2
        float[] coords2 = spawnCoords[1];
        TeleportService.teleportTo(player2, instance.getMapId(), instance.getInstanceId(),
            coords2[0], coords2[1], coords2[2], (byte) coords2[3], TeleportAnimation.FADE_OUT_BEAM);

        log.debug("Teleported players {} and {} to OneVsOne match on map {} at different spawn points: " +
            "Player1({}, {}, {}) Player2({}, {}, {})",
            player1.getName(), player2.getName(), instance.getMapId(),
            coords1[0], coords1[1], coords1[2], coords2[0], coords2[1], coords2[2]);
    }

    /**
     * Teleport player to match instance (legacy method - kept for compatibility)
     */
    private void teleportToMatch(Player player, WorldMapInstance instance) {
        float x, y, z;
        byte heading = 0;

        // Use fallback coordinates for legacy single-player teleport
        switch (instance.getMapId()) {
            case 300030000: // Nochsana Training Camp
                x = 517.9041f;
                y = 629.4911f;
                z = 330.35507f;
                break;
            case 300040000: // Dark Poeta
                x = 242.52405f;
                y = 424.71637f;
                z = 103.80612f;
                break;
            case 300250000: // Esoterrace
                x = 844.81775f;
                y = 578.80804f;
                z = 180.85222f;
                break;
            default:
                // Fallback coordinates for unknown maps
                log.warn("Unknown map ID {} for OneVsOne teleport, using fallback coordinates", instance.getMapId());
                x = 500.0f;
                y = 500.0f;
                z = 500.0f;
                break;
        }

        TeleportService.teleportTo(player, instance.getMapId(), instance.getInstanceId(),
            x, y, z, heading, TeleportAnimation.FADE_OUT_BEAM);

        log.debug("Teleported player {} to OneVsOne match on map {} at coordinates ({}, {}, {})",
            player.getName(), instance.getMapId(), x, y, z);
    }

    /**
     * End match by timeout
     */
    private void endMatchByTimeout(OneVsOneParticipant participant1, OneVsOneParticipant participant2) {
        log.info("OneVsOne match timed out: {} vs {}", participant1.getPlayerName(), participant2.getPlayerName());

        Player player1 = World.getInstance().getPlayer(participant1.getPlayerId());
        Player player2 = World.getInstance().getPlayer(participant2.getPlayerId());

        if (player1 != null) {
            PacketSendUtility.sendMessage(player1, "OneVsOne match ended due to timeout. No winner.", ChatType.BRIGHT_YELLOW_CENTER);
            unsetOneVsOneParticipantState(player1);
            teleportPlayerToPortalLocation(player1);
        }

        if (player2 != null) {
            PacketSendUtility.sendMessage(player2, "OneVsOne match ended due to timeout. No winner.", ChatType.BRIGHT_YELLOW_CENTER);
            unsetOneVsOneParticipantState(player2);
            teleportPlayerToPortalLocation(player2);
        }

        // Send participation rewards to both players
        sendRewards(participant1, false);
        sendRewards(participant2, false);

        // Clean up match data
        cleanupMatch(participant1, participant2);
    }

    /**
     * End match with winner (used for timeout scenarios - kill scenarios are handled in onPlayerKill)
     */
    public void endMatchWithWinner(Player winner, Player loser) {
        log.info("DEBUG: endMatchWithWinner called - Winner: {}, Loser: {}", winner.getName(), loser.getName());

        OneVsOneParticipant winnerParticipant = activePlayers.get(winner.getObjectId());
        OneVsOneParticipant loserParticipant = activePlayers.get(loser.getObjectId());

        if (winnerParticipant == null || loserParticipant == null) {
            log.warn("DEBUG: Attempted to end OneVsOne match with invalid participants - Winner participant: {}, Loser participant: {}",
                winnerParticipant != null ? "FOUND" : "NULL", loserParticipant != null ? "FOUND" : "NULL");
            return;
        }

        log.info("OneVsOne match ended: {} defeated {}", winner.getName(), loser.getName());

        // Update statistics
        winnerParticipant.addWin();
        loserParticipant.addLoss();

        // Send final messages
        PacketSendUtility.sendMessage(winner,
            String.format("Victory! You defeated %s in OneVsOne combat! Rewards sent via mail.", loser.getName()),
            ChatType.BRIGHT_YELLOW_CENTER);

        PacketSendUtility.sendMessage(loser,
            String.format("Good fight! You were defeated by %s. Participation rewards sent via mail.", winner.getName()),
            ChatType.BRIGHT_YELLOW_CENTER);

        // Remove OneVsOne participant states
        unsetOneVsOneParticipantState(winner);
        unsetOneVsOneParticipantState(loser);

        // Teleport players back to portal locations
        teleportPlayerToPortalLocation(winner);
        teleportPlayerToPortalLocation(loser);

        // Send rewards
        sendRewards(winnerParticipant, true);
        sendRewards(loserParticipant, false);

        // Clean up match data
        cleanupMatch(winnerParticipant, loserParticipant);

        // Announce if enabled
        if (CustomConfig.ONEVSONE_ANNOUNCEMENTS) {
            World.getInstance().forEachPlayer(player -> {
                PacketSendUtility.sendMessage(player,
                    String.format("OneVsOne: %s defeated %s!", winner.getName(), loser.getName()),
                    ChatType.BRIGHT_YELLOW_CENTER);
            });
        }
    }

    /**
     * Clean up match data
     */
    private void cleanupMatch(OneVsOneParticipant participant1, OneVsOneParticipant participant2) {
        // Remove from active players
        activePlayers.remove(participant1.getPlayerId());
        activePlayers.remove(participant2.getPlayerId());

        // Clean up death tracking
        playerDeathTimes.remove(participant1.getPlayerId());
        playerDeathTimes.remove(participant2.getPlayerId());
        
        // Clean up offline tracking
        playerOfflineTimes.remove(participant1.getPlayerId());
        playerOfflineTimes.remove(participant2.getPlayerId());

        // Reset match data
        participant1.resetMatchData();
        participant2.resetMatchData();

        // Send message to players about re-queueing
        Player player1 = World.getInstance().getPlayer(participant1.getPlayerId());
        Player player2 = World.getInstance().getPlayer(participant2.getPlayerId());

        if (player1 != null) {
            PacketSendUtility.sendMessage(player1,
                "You can now re-queue for another OneVsOne match by using the portal again!",
                ChatType.BRIGHT_YELLOW_CENTER);
        }

        if (player2 != null) {
            PacketSendUtility.sendMessage(player2,
                "You can now re-queue for another OneVsOne match by using the portal again!",
                ChatType.BRIGHT_YELLOW_CENTER);
        }

        log.debug("Cleaned up OneVsOne match data for players {} and {}",
            participant1.getPlayerName(), participant2.getPlayerName());
    }

    /**
     * Teleport player to their bind point
     */
    private void teleportPlayerToBindPoint(Player player) {
        TeleportService.moveToBindLocation(player);
    }

    /**
     * Clear all monsters from the OneVsOne instance to create a clean PvP environment
     */
    private void clearInstanceMonsters(WorldMapInstance instance) {
        if (instance == null) {
            return;
        }

        try {
            int clearedCount = 0;

            // Get all NPCs in the instance and despawn them
            for (Npc npc : instance.getNpcs()) {
                if (npc != null && !npc.isDead()) {
                    try {
                        // Despawn the NPC
                        npc.getController().delete();
                        clearedCount++;
                        log.debug("Despawned NPC {} from OneVsOne instance", npc.getNpcId());
                    } catch (Exception e) {
                        log.debug("Failed to despawn NPC {}: {}", npc.getNpcId(), e.getMessage());
                    }
                }
            }

            log.debug("Cleared {} monsters from OneVsOne instance {} - Clean PvP environment ready!",
                clearedCount, instance.getInstanceId());

        } catch (Exception e) {
            log.warn("Failed to clear monsters from OneVsOne instance: {}", e.getMessage());
        }
    }

    /**
     * Get participant by player ID (searches both queue and active matches)
     */
    private OneVsOneParticipant getParticipantByPlayerId(int playerId) {
        // Check queued players first
        OneVsOneParticipant participant = queuedPlayers.get(playerId);
        if (participant != null) {
            return participant;
        }

        // Check active matches
        Integer matchInstanceId = playerToMatch.get(playerId);
        if (matchInstanceId != null) {
            OneVsOneMatch match = activeMatches.get(matchInstanceId);
            if (match != null) {
                return match.getParticipant(playerId);
            }
        }

        return null;
    }

    /**
     * Teleport player back to their original registration location
     */
    private void teleportPlayerToPortalLocation(Player player) {
        try {
            // Try to get the participant's original registration location
            OneVsOneParticipant participant = getParticipantByPlayerId(player.getObjectId());

            if (participant != null) {
                // Use stored registration location
                int worldId = participant.getRegistrationWorldId();
                float x = participant.getRegistrationX();
                float y = participant.getRegistrationY();
                float z = participant.getRegistrationZ();
                byte heading = participant.getRegistrationHeading();

                log.info("DEBUG: Teleporting {} back to original registration location: world {}, coords {}, {}, {}, heading {}",
                    player.getName(), worldId, x, y, z, heading);

                // Teleport to original registration location with animation
                TeleportService.teleportTo(player, worldId, 0, x, y, z, heading, TeleportAnimation.FADE_OUT_BEAM);

                // Send message to player
                PacketSendUtility.sendMessage(player,
                    "You have been teleported back to where you registered for OneVsOne.",
                    ChatType.BRIGHT_YELLOW_CENTER);
                return;
            }

            // Fallback: Parse portal locations from configuration if participant not found
            String[] locations = CustomConfig.ONEVSONE_PORTAL_LOCATIONS.split(";");

            for (String location : locations) {
                String[] parts = location.split(":");
                int worldId = Integer.parseInt(parts[0]);
                String[] coords = parts[1].split(",");
                float x = Float.parseFloat(coords[0]);
                float y = Float.parseFloat(coords[1]);
                float z = Float.parseFloat(coords[2]);
                byte heading = Byte.parseByte(coords[3]);

                // Check if this location matches player's race
                boolean isSanctum = worldId == 110010000; // Sanctum
                boolean isPandaemonium = worldId == 120010000; // Pandaemonium

                if ((player.getRace() == Race.ELYOS && isSanctum) ||
                    (player.getRace() == Race.ASMODIANS && isPandaemonium)) {

                    log.info("DEBUG: Teleporting {} back to fallback portal location: world {}, instance 0, coords {}, {}, {}, heading {}",
                        player.getName(), worldId, x, y, z, heading);

                    // Teleport to main world instance (0) with animation
                    TeleportService.teleportTo(player, worldId, 0, x, y, z, heading, TeleportAnimation.FADE_OUT_BEAM);

                    // Send message to player
                    PacketSendUtility.sendMessage(player,
                        "You have been teleported back to the OneVsOne portal area.",
                        ChatType.BRIGHT_YELLOW_CENTER);
                    return;
                }
            }

            // Final fallback to bind point if nothing else works
            log.warn("Could not find appropriate portal location for player {}, using bind point", player.getName());
            TeleportService.moveToBindLocation(player);

        } catch (Exception e) {
            log.error("Error teleporting player {} to portal location, using bind point", player.getName(), e);
            TeleportService.moveToBindLocation(player);
        }
    }

    /**
     * Verify that portal NPCs are present at the specified location (for debugging)
     */
    private void verifyPortalNpcsAtLocation(int worldId, float x, float y, float z) {
        try {
            // Check if any of our spawned portal NPCs are at this location
            boolean foundPortal = false;
            for (Npc portalNpc : portalNpcs) {
                if (portalNpc != null && portalNpc.getWorldId() == worldId) {
                    float distance = (float) Math.sqrt(
                        Math.pow(portalNpc.getX() - x, 2) +
                        Math.pow(portalNpc.getY() - y, 2) +
                        Math.pow(portalNpc.getZ() - z, 2)
                    );

                    if (distance < 50.0f) { // Within 50 units
                        foundPortal = true;
                        log.debug("Portal NPC {} found at destination - Distance: {:.2f}, Spawned: {}, InWorld: {}",
                                 portalNpc.getObjectId(), distance, portalNpc.isSpawned(), portalNpc.isInWorld());
                        break;
                    }
                }
            }

            if (!foundPortal) {
                log.warn("No portal NPC found near teleport destination at world {} coords {}, {}, {} - Players may see empty area!",
                        worldId, x, y, z);
                log.warn("Total spawned portals: {}", portalNpcs.size());

                // List all portal locations for debugging
                for (Npc portalNpc : portalNpcs) {
                    if (portalNpc != null) {
                        log.warn("Portal NPC {} at world {} coords {}, {}, {} - Spawned: {}",
                                portalNpc.getObjectId(), portalNpc.getWorldId(),
                                portalNpc.getX(), portalNpc.getY(), portalNpc.getZ(), portalNpc.isSpawned());
                    }
                }

                // Attempt to respawn missing portal at this location
                log.info("Attempting to respawn missing portal at world {} coords {}, {}, {}", worldId, x, y, z);
                try {
                    SpawnTemplate spawn = SpawnEngine.newSingleTimeSpawn(worldId, CustomConfig.ONEVSONE_PORTAL_NPC,
                        x, y, z, (byte) 0, 0, "onevsone_portal_respawn");
                    Npc portalNpc = (Npc) SpawnEngine.spawnObject(spawn, 0);
                    if (portalNpc != null) {
                        portalNpcs.add(portalNpc);
                        log.info("Successfully respawned missing OneVsOne portal at world {} coords {}, {}, {}",
                                worldId, x, y, z);
                    }
                } catch (Exception e) {
                    log.error("Failed to respawn missing portal: {}", e.getMessage());
                }
            }
        } catch (Exception e) {
            log.error("Error verifying portal NPCs at location: {}", e.getMessage());
        }
    }

    /**
     * Send rewards to participant
     */
    private void sendRewards(OneVsOneParticipant participant, boolean isWinner) {
        String rewardConfig = isWinner ? CustomConfig.ONEVSONE_REWARDS_WINNER : CustomConfig.ONEVSONE_REWARDS_LOSER;

        log.info("DEBUG: Sending OneVsOne rewards to player {} ({})", participant.getPlayerName(), isWinner ? "WINNER" : "LOSER");
        log.info("DEBUG: Reward config string: {}", rewardConfig);

        int[] rewardData = parseRewardConfig(rewardConfig);

        log.info("DEBUG: Parsed reward data - ItemID: {}, Count: {}, Kinah: {}",
            rewardData[0], rewardData[1], rewardData[2]);

        String title = "OneVsOne Match Rewards";
        String message = isWinner ?
            String.format("Congratulations! You won your OneVsOne match! Total wins: %d, Total matches: %d",
                participant.getWins(), participant.getTotalMatches()) :
            String.format("You participated in a OneVsOne match. Total wins: %d, Total matches: %d",
                participant.getWins(), participant.getTotalMatches());

        // Send reward via mail (itemId, itemCount, kinah)
        boolean mailSent = SystemMailService.sendMail("OneVsOne System", participant.getPlayerName(), title, message,
            rewardData[0], rewardData[1], rewardData[2], LetterType.EXPRESS);

        if (mailSent) {
            log.info("DEBUG: Successfully sent {} rewards to player {} - ItemID: {}, Count: {}, Kinah: {}",
                isWinner ? "winner" : "loser", participant.getPlayerName(), rewardData[0], rewardData[1], rewardData[2]);
        } else {
            log.error("DEBUG: FAILED to send {} rewards to player {} - ItemID: {}, Count: {}, Kinah: {}",
                isWinner ? "winner" : "loser", participant.getPlayerName(), rewardData[0], rewardData[1], rewardData[2]);
        }
    }

    /**
     * Parse reward configuration string
     * Format: itemId:itemCount:kinah
     */
    private int[] parseRewardConfig(String config) {
        String[] parts = config.split(":");
        int[] values = new int[parts.length];
        for (int i = 0; i < parts.length; i++) {
            values[i] = Integer.parseInt(parts[i]);
        }
        return values;
    }

    /**
     * Clean up queue by removing timed out players
     */
    private void cleanupQueue() {
        long currentTime = System.currentTimeMillis();
        long timeoutMs = CustomConfig.ONEVSONE_QUEUE_TIMEOUT_MINUTES * 60 * 1000;

        queuedPlayers.entrySet().removeIf(entry -> {
            OneVsOneParticipant participant = entry.getValue();
            if (currentTime - participant.getQueueJoinTime() > timeoutMs) {
                Player player = World.getInstance().getPlayer(participant.getPlayerId());
                if (player != null) {
                    PacketSendUtility.sendMessage(player,
                        "You have been removed from the OneVsOne queue due to timeout.",
                        ChatType.BRIGHT_YELLOW_CENTER);
                }
                log.debug("Removed player {} from OneVsOne queue due to timeout", participant.getPlayerName());
                return true;
            }
            return false;
        });
    }

    /**
     * Remove all players from queue
     */
    private void removeAllPlayersFromQueue() {
        for (OneVsOneParticipant participant : queuedPlayers.values()) {
            Player player = World.getInstance().getPlayer(participant.getPlayerId());
            if (player != null) {
                PacketSendUtility.sendMessage(player,
                    "OneVsOne system has been stopped. You have been removed from the queue.",
                    ChatType.BRIGHT_YELLOW_CENTER);
            }
        }
        queuedPlayers.clear();
        log.info("Removed all players from OneVsOne queue");
    }

    /**
     * End all active matches
     */
    private void endAllActiveMatches() {
        log.info("Ending all active OneVsOne matches due to system stop...");

        // First, handle all active matches properly
        for (OneVsOneMatch match : new ArrayList<>(activeMatches.values())) {
            log.debug("Ending active match: {}", match.getInstanceId());

            // Get both participants
            OneVsOneParticipant participant1 = match.getPlayer1();
            OneVsOneParticipant participant2 = match.getPlayer2();

            // Get players
            Player player1 = participant1 != null ? World.getInstance().getPlayer(participant1.getPlayerId()) : null;
            Player player2 = participant2 != null ? World.getInstance().getPlayer(participant2.getPlayerId()) : null;

            // Notify and teleport players using the same logic as normal match end
            if (player1 != null) {
                PacketSendUtility.sendMessage(player1,
                    "OneVsOne system has been stopped. Your match has ended.",
                    ChatType.BRIGHT_YELLOW_CENTER);
                resurrectPlayerForTeleportation(player1);
                unsetOneVsOneParticipantState(player1);
                teleportPlayerToPortalLocation(player1);

                // Second teleportation after delay to clear any remaining dialogs
                ThreadPoolManager.getInstance().schedule(() -> {
                    if (player1.isOnline()) {
                        teleportPlayerToPortalLocation(player1);
                        log.debug("Second teleportation completed for player1 {} during system stop", player1.getName());
                    }
                }, 1500);
            }

            if (player2 != null) {
                PacketSendUtility.sendMessage(player2,
                    "OneVsOne system has been stopped. Your match has ended.",
                    ChatType.BRIGHT_YELLOW_CENTER);
                resurrectPlayerForTeleportation(player2);
                unsetOneVsOneParticipantState(player2);
                teleportPlayerToPortalLocation(player2);

                // Second teleportation after delay to clear any remaining dialogs
                ThreadPoolManager.getInstance().schedule(() -> {
                    if (player2.isOnline()) {
                        teleportPlayerToPortalLocation(player2);
                        log.debug("Second teleportation completed for player2 {} during system stop", player2.getName());
                    }
                }, 1500);
            }

            // Clean up match data
            if (participant1 != null && participant2 != null) {
                cleanupMatch(participant1, participant2);
            }
        }

        // Store count before clearing
        int totalMatchesEnded = activeMatches.size();

        // Clear all collections
        activeMatches.clear();
        playerToMatch.clear();
        activePlayers.clear();
        playerDeathTimes.clear();
        playerOfflineTimes.clear();

        log.info("Ended all active OneVsOne matches - Total matches ended: {}", totalMatchesEnded);
    }

    // Public getters for admin commands and status

    public boolean isSystemActive() {
        return systemActive;
    }

    public int getQueuedPlayersCount() {
        return queuedPlayers.size();
    }

    public int getActivePlayersCount() {
        return activePlayers.size();
    }

    public int getQueueSize() {
        return queuedPlayers.size();
    }

    public int getActiveMatchCount() {
        return activeMatches.size();
    }

    public Map<Integer, OneVsOneParticipant> getQueuedPlayers() {
        return new HashMap<>(queuedPlayers);
    }

    public Map<Integer, OneVsOneParticipant> getActivePlayers() {
        return new HashMap<>(activePlayers);
    }

    /**
     * Get participant statistics
     */
    public OneVsOneParticipant getParticipantStats(int playerId) {
        OneVsOneParticipant participant = queuedPlayers.get(playerId);
        if (participant == null) {
            participant = activePlayers.get(playerId);
        }
        return participant;
    }

    /**
     * Manually fix a player's death state (admin command)
     */
    public boolean manuallyFixDeathState(int playerId) {
        OneVsOneParticipant participant = activePlayers.get(playerId);
        if (participant == null) {
            log.warn("Cannot fix death state - player {} not in active OneVsOne match", playerId);
            return false;
        }

        Player player = World.getInstance().getPlayer(playerId);
        if (player == null || !player.isOnline()) {
            log.warn("Cannot fix death state - player {} not found or offline", playerId);
            return false;
        }

        if (!player.isDead()) {
            log.warn("Cannot fix death state - player {} is not dead", player.getName());
            return false;
        }

        log.info("Manually fixing death state for player {} via admin command", player.getName());
        autoFixStuckDeathState(player, participant);
        return true;
    }

    /**
     * Force remove player from queue or match (admin command)
     */
    public boolean forceRemovePlayer(int playerId) {
        OneVsOneParticipant participant = queuedPlayers.remove(playerId);
        if (participant != null) {
            Player player = World.getInstance().getPlayer(playerId);
            if (player != null) {
                PacketSendUtility.sendMessage(player,
                    "You have been removed from the OneVsOne queue by an administrator.",
                    ChatType.BRIGHT_YELLOW_CENTER);
            }
            return true;
        }

        participant = activePlayers.remove(playerId);
        if (participant != null) {
            Player player = World.getInstance().getPlayer(playerId);
            if (player != null) {
                PacketSendUtility.sendMessage(player,
                    "Your OneVsOne match has been ended by an administrator.",
                    ChatType.BRIGHT_YELLOW_CENTER);
                unsetOneVsOneParticipantState(player);
                teleportPlayerToPortalLocation(player);
            }

            // Clean up death tracking
            playerDeathTimes.remove(playerId);
            playerOfflineTimes.remove(playerId);

            // Also remove opponent
            OneVsOneParticipant opponent = participant.getCurrentOpponent();
            if (opponent != null) {
                activePlayers.remove(opponent.getPlayerId());
                Player opponentPlayer = World.getInstance().getPlayer(opponent.getPlayerId());
                if (opponentPlayer != null) {
                    PacketSendUtility.sendMessage(opponentPlayer,
                        "Your OneVsOne match has been ended by an administrator.",
                        ChatType.BRIGHT_YELLOW_CENTER);
                    unsetOneVsOneParticipantState(opponentPlayer);
                    teleportPlayerToPortalLocation(opponentPlayer);
                }

                // Clean up death tracking for opponent
                playerDeathTimes.remove(opponent.getPlayerId());
                playerOfflineTimes.remove(opponent.getPlayerId());
                opponent.resetMatchData();
            }

            participant.resetMatchData();
            return true;
        }

        return false;
    }

    /**
     * Set OneVsOne participant state for a player (enables same-race PvP)
     */
    private void setOneVsOneParticipantState(Player player) {
        // Set OneVsOne participant state
        player.setCustomState(com.aionemu.gameserver.model.gameobjects.player.CustomPlayerState.ONEVSONE_PARTICIPANT);

        // Also set as enemy of all players to force client-side attackability (bypass client restrictions)
        player.setCustomState(com.aionemu.gameserver.model.gameobjects.player.CustomPlayerState.ENEMY_OF_ALL_PLAYERS);

        // Update player attributes to inform other players about the state change
        player.getController().onChangedPlayerAttributes();

        log.debug("Set OneVsOne participant state for player {}", player.getName());

        // Remove any teleportation protection after a short delay to ensure PvP works immediately
        ThreadPoolManager.getInstance().schedule(() -> {
            if (player.isOnline() && activePlayers.containsKey(player.getObjectId())) {
                // Remove invulnerability and teleportation protection states
                player.unsetCustomState(com.aionemu.gameserver.model.gameobjects.player.CustomPlayerState.INVULNERABLE);
                player.unsetCustomState(com.aionemu.gameserver.model.gameobjects.player.CustomPlayerState.TELEPORTATION_MODE);
                player.getController().onChangedPlayerAttributes();
                log.debug("Removed teleportation protection for OneVsOne player {}", player.getName());
            }
        }, 3000); // 3 second delay
    }

    /**
     * Unset OneVsOne participant state for a player
     */
    private void unsetOneVsOneParticipantState(Player player) {
        // Remove OneVsOne participant state
        player.unsetCustomState(com.aionemu.gameserver.model.gameobjects.player.CustomPlayerState.ONEVSONE_PARTICIPANT);

        // Also remove enemy of all players state
        player.unsetCustomState(com.aionemu.gameserver.model.gameobjects.player.CustomPlayerState.ENEMY_OF_ALL_PLAYERS);

        // Update player attributes to inform other players about the state change
        player.getController().onChangedPlayerAttributes();

        log.debug("Unset OneVsOne participant state for player {}", player.getName());
    }

    /**
     * Handle player kill in OneVsOne match - Round-based system
     */
    public void onPlayerKill(Player killer, Player victim) {
        // Get the match for this kill
        Integer matchInstanceId = playerToMatch.get(victim.getObjectId());
        if (matchInstanceId == null) {
            log.debug("OneVsOne kill ignored - victim not in active match");
            return;
        }

        OneVsOneMatch match = activeMatches.get(matchInstanceId);
        if (match == null) {
            log.debug("OneVsOne kill ignored - match not found");
            return;
        }

        // Verify both players are in this match
        if (!match.hasPlayer(killer.getObjectId()) || !match.hasPlayer(victim.getObjectId())) {
            log.debug("OneVsOne kill ignored - players not in same match");
            return;
        }

        // Only process kills during fighting state
        if (match.getState() != OneVsOneMatch.MatchState.FIGHTING) {
            log.debug("OneVsOne kill ignored - match not in fighting state");
            return;
        }

        log.info("OneVsOne round kill detected: {} killed {} in round {}",
                killer.getName(), victim.getName(), match.getCurrentRound());

        // Award round to killer
        OneVsOneParticipant killerParticipant = match.getParticipant(killer.getObjectId());
        OneVsOneParticipant victimParticipant = match.getParticipant(victim.getObjectId());

        if (killerParticipant == match.getPlayer1()) {
            match.addScoreToPlayer1();
        } else {
            match.addScoreToPlayer2();
        }

        // Send round victory message
        String scoreMessage = String.format("✅ %s-%s for %s",
                match.getPlayer1() == killerParticipant ? match.getPlayer1Score() : match.getPlayer2Score(),
                match.getPlayer1() == killerParticipant ? match.getPlayer2Score() : match.getPlayer1Score(),
                killer.getName());

        PacketSendUtility.sendMessage(killer, scoreMessage, ChatType.BRIGHT_YELLOW_CENTER);
        PacketSendUtility.sendMessage(victim, scoreMessage, ChatType.BRIGHT_YELLOW_CENTER);

        // Check if match is complete
        if (match.isMatchComplete()) {
            // Match is over
            handleMatchEnd(match);
        } else {
            // Prepare for next round
            handleRoundEnd(match);
        }
    }

    /**
     * Handle the end of a round (not the entire match)
     */
    private void handleRoundEnd(OneVsOneMatch match) {
        match.setState(OneVsOneMatch.MatchState.ROUND_END);

        // Check if participants exist
        if (match.getPlayer1() == null || match.getPlayer2() == null) {
            log.warn("Match participants are null during round end, ending match");
            cleanupMatchWithoutWinner(match);
            return;
        }

        Player player1 = World.getInstance().getPlayer(match.getPlayer1().getPlayerId());
        Player player2 = World.getInstance().getPlayer(match.getPlayer2().getPlayerId());

        if (player1 == null || player2 == null) {
            log.warn("Player disconnected during round end, ending match");
            cleanupMatchWithoutWinner(match);
            return;
        }

        // Resurrect both players with full HP/MP and reset cooldowns
        resurrectPlayerForNextRound(player1, match);
        resurrectPlayerForNextRound(player2, match);

        // Schedule next round after 3 seconds
        ThreadPoolManager.getInstance().schedule(() -> {
            startNextRound(match);
        }, 3000);
    }

    /**
     * Handle the complete end of a match
     */
    private void handleMatchEnd(OneVsOneMatch match) {
        match.setState(OneVsOneMatch.MatchState.MATCH_END);

        OneVsOneParticipant winner = match.getWinner();
        OneVsOneParticipant loser = match.getLoser();

        // Handle case where match ends without a clear winner (e.g., player disconnect)
        if (winner == null || loser == null) {
            log.warn("Match ended without clear winner/loser - cleaning up match {}", match.getInstanceId());
            cleanupMatchWithoutWinner(match);
            return;
        }

        Player winnerPlayer = World.getInstance().getPlayer(winner.getPlayerId());
        Player loserPlayer = World.getInstance().getPlayer(loser.getPlayerId());

        // Update statistics
        winner.addWin();
        loser.addLoss();

        // Send final messages
        if (winnerPlayer != null) {
            PacketSendUtility.sendMessage(winnerPlayer,
                String.format("🏆 VICTORY! You defeated %s %s! Rewards sent via mail.",
                    loser.getPlayerName(), match.getScoreString()),
                ChatType.BRIGHT_YELLOW_CENTER);
        }

        if (loserPlayer != null) {
            PacketSendUtility.sendMessage(loserPlayer,
                String.format("💀 DEFEAT! %s defeated you %s. Better luck next time!",
                    winner.getPlayerName(), match.getScoreString()),
                ChatType.BRIGHT_YELLOW_CENTER);
        }

        // Send teleportation messages to both players
        if (winnerPlayer != null) {
            PacketSendUtility.sendMessage(winnerPlayer,
                "You will be teleported back to the portal in 3 seconds...",
                ChatType.BRIGHT_YELLOW_CENTER);
        }

        if (loserPlayer != null) {
            PacketSendUtility.sendMessage(loserPlayer,
                "You will be teleported back to the portal in 3 seconds...",
                ChatType.BRIGHT_YELLOW_CENTER);
        }

        // Schedule cleanup and teleportation
        ThreadPoolManager.getInstance().schedule(() -> {
            // Resurrect dead players before teleportation to prevent getting stuck
            if (winnerPlayer != null) {
                resurrectPlayerForTeleportation(winnerPlayer);
                unsetOneVsOneParticipantState(winnerPlayer);
                // First teleportation
                teleportPlayerToPortalLocation(winnerPlayer);
                PacketSendUtility.sendMessage(winnerPlayer,
                    "Teleported back to OneVsOne portal.",
                    ChatType.BRIGHT_YELLOW_CENTER);

                // Second teleportation after delay to clear any remaining dialogs (like entry process)
                ThreadPoolManager.getInstance().schedule(() -> {
                    if (winnerPlayer.isOnline()) {
                        teleportPlayerToPortalLocation(winnerPlayer);
                        log.debug("Second teleportation completed for winner {}", winnerPlayer.getName());
                    }
                }, 1500);
            }

            if (loserPlayer != null) {
                resurrectPlayerForTeleportation(loserPlayer);
                unsetOneVsOneParticipantState(loserPlayer);
                // First teleportation
                teleportPlayerToPortalLocation(loserPlayer);
                PacketSendUtility.sendMessage(loserPlayer,
                    "Teleported back to OneVsOne portal.",
                    ChatType.BRIGHT_YELLOW_CENTER);

                // Second teleportation after delay to clear any remaining dialogs (like entry process)
                ThreadPoolManager.getInstance().schedule(() -> {
                    if (loserPlayer.isOnline()) {
                        teleportPlayerToPortalLocation(loserPlayer);
                        log.debug("Second teleportation completed for loser {}", loserPlayer.getName());
                    }
                }, 1500);
            }

            // Clean up match data after teleportation starts to prevent interference
            cleanupMatch(winner, loser);
            activeMatches.remove(match.getInstanceId());
            playerToMatch.remove(winner.getPlayerId());
            playerToMatch.remove(loser.getPlayerId());

            // Send rewards
            sendRewards(winner, true);
            sendRewards(loser, false);

            // Announce if enabled
            if (CustomConfig.ONEVSONE_ANNOUNCEMENTS) {
                World.getInstance().forEachPlayer(player -> {
                    PacketSendUtility.sendMessage(player,
                        String.format("OneVsOne: %s defeated %s %s!",
                            winner.getPlayerName(), loser.getPlayerName(), match.getScoreString()),
                        ChatType.BRIGHT_YELLOW_CENTER);
                });
            }

            log.info("OneVsOne match completed: {} defeated {} {}",
                    winner.getPlayerName(), loser.getPlayerName(), match.getScoreString());
        }, 3000); // 3 second delay
    }

    /**
     * Check if player is in an active match
     */
    public boolean isPlayerInActiveMatch(Player player) {
        return playerToMatch.containsKey(player.getObjectId());
    }

    /**
     * Get player's match instance ID
     */
    public Integer getPlayerMatchInstanceId(int playerId) {
        return playerToMatch.get(playerId);
    }

    /**
     * Get active match by instance ID
     */
    public OneVsOneMatch getActiveMatch(Integer instanceId) {
        return activeMatches.get(instanceId);
    }

    /**
     * Handle player disconnect/logout (called from PlayerLeaveWorldService)
     */
    public void handlePlayerDisconnect(Player player) {
        // Check if player is in queue first
        if (queuedPlayers.containsKey(player.getObjectId())) {
            queuedPlayers.remove(player.getObjectId());
            log.debug("Removed disconnected player {} from OneVsOne queue", player.getName());
        }

        // Check if player is in an active match
        Integer matchInstanceId = playerToMatch.get(player.getObjectId());
        if (matchInstanceId != null) {
            OneVsOneMatch match = activeMatches.get(matchInstanceId);
            if (match != null) {
                log.info("Player {} disconnected from OneVsOne match, awarding victory to opponent", player.getName());
                
                // Track offline time
                playerOfflineTimes.put(player.getObjectId(), System.currentTimeMillis());

                // Award victory to the remaining player and handle cleanup
                handlePlayerDisconnectFromMatch(player, match);
            } else {
                // Match was already cleaned up, just remove player mapping
                playerToMatch.remove(player.getObjectId());
                activePlayers.remove(player.getObjectId());
                log.debug("Disconnected player {} was in playerToMatch but match was already cleaned up", player.getName());
            }
        }
    }

    /**
     * Handle player disconnect from an active match
     */
    private void handlePlayerDisconnectFromMatch(Player disconnectedPlayer, OneVsOneMatch match) {
        // Check if match is already ended
        if (match.getState() == OneVsOneMatch.MatchState.MATCH_END) {
            // Match already ended, just clean up
            cleanupMatchDataOnly(match);
            return;
        }

        // Get participants
        OneVsOneParticipant disconnectedParticipant = match.getParticipant(disconnectedPlayer.getObjectId());
        OneVsOneParticipant remainingParticipant = match.getOpponent(disconnectedPlayer.getObjectId());

        if (disconnectedParticipant != null && remainingParticipant != null) {
            // Award victory to remaining player
            if (remainingParticipant == match.getPlayer1()) {
                match.getPlayer1().addWin();
                match.getPlayer2().addLoss();
            } else {
                match.getPlayer2().addWin();
                match.getPlayer1().addLoss();
            }

            // Handle remaining player
            Player remainingPlayerObj = World.getInstance().getPlayer(remainingParticipant.getPlayerId());
            if (remainingPlayerObj != null) {
                PacketSendUtility.sendMessage(remainingPlayerObj,
                    "Your opponent disconnected. You win by default!",
                    ChatType.BRIGHT_YELLOW_CENTER);

                // Resurrect if dead and teleport remaining player back to portal
                resurrectPlayerForTeleportation(remainingPlayerObj);
                unsetOneVsOneParticipantState(remainingPlayerObj);

                // First teleportation
                teleportPlayerToPortalLocation(remainingPlayerObj);
                PacketSendUtility.sendMessage(remainingPlayerObj,
                    "You will be teleported back to the portal in 3 seconds...",
                    ChatType.BRIGHT_YELLOW_CENTER);

                // Second teleportation after delay to clear any remaining dialogs
                ThreadPoolManager.getInstance().schedule(() -> {
                    if (remainingPlayerObj.isOnline()) {
                        teleportPlayerToPortalLocation(remainingPlayerObj);
                        log.debug("Second teleportation completed for remaining player {} after opponent disconnect", remainingPlayerObj.getName());
                    }
                }, 1500);
            }

            // Send rewards to both players
            sendRewards(remainingParticipant, true);  // Winner gets rewards
            sendRewards(disconnectedParticipant, false);  // Loser gets participation rewards

            log.info("OneVsOne match ended due to disconnect - Winner: {}, Disconnected: {}",
                    remainingParticipant.getPlayerName(), disconnectedParticipant.getPlayerName());
        }

        // Clean up match data
        cleanupMatchDataOnly(match);
    }

    /**
     * Handle player leaving instance
     */
    public void handlePlayerLeave(Player player) {
        Integer matchInstanceId = playerToMatch.get(player.getObjectId());
        if (matchInstanceId != null) {
            OneVsOneMatch match = activeMatches.get(matchInstanceId);
            if (match != null) {
                log.info("Player {} left OneVsOne match, ending match", player.getName());

                // Check if match is still in progress and has a clear state
                if (match.getState() == OneVsOneMatch.MatchState.MATCH_END) {
                    // Match already ended, just clean up without teleporting
                    cleanupMatchDataOnly(match);
                } else {
                    // Award victory to the remaining player if match was in progress
                    OneVsOneParticipant leavingPlayer = match.getParticipant(player.getObjectId());
                    OneVsOneParticipant remainingPlayer = match.getOpponent(player.getObjectId());

                    if (leavingPlayer != null && remainingPlayer != null) {
                        // Award victory to remaining player
                        if (remainingPlayer == match.getPlayer1()) {
                            match.getPlayer1().addWin();
                            match.getPlayer2().addLoss();
                        } else {
                            match.getPlayer2().addWin();
                            match.getPlayer1().addLoss();
                        }

                        Player remainingPlayerObj = World.getInstance().getPlayer(remainingPlayer.getPlayerId());
                        if (remainingPlayerObj != null) {
                            PacketSendUtility.sendMessage(remainingPlayerObj,
                                "Your opponent left the match. You win by default!",
                                ChatType.BRIGHT_YELLOW_CENTER);

                            // Resurrect if dead and teleport remaining player back to portal
                            resurrectPlayerForTeleportation(remainingPlayerObj);
                            unsetOneVsOneParticipantState(remainingPlayerObj);

                            // First teleportation
                            teleportPlayerToPortalLocation(remainingPlayerObj);
                            PacketSendUtility.sendMessage(remainingPlayerObj,
                                "You will be teleported back to the portal in 3 seconds...",
                                ChatType.BRIGHT_YELLOW_CENTER);

                            // Second teleportation after delay to clear any remaining dialogs
                            ThreadPoolManager.getInstance().schedule(() -> {
                                if (remainingPlayerObj.isOnline()) {
                                    teleportPlayerToPortalLocation(remainingPlayerObj);
                                    log.debug("Second teleportation completed for remaining player {} after opponent left", remainingPlayerObj.getName());
                                }
                            }, 1500);
                        }

                        // Send rewards to both players
                        sendRewards(remainingPlayer, true);  // Winner gets rewards
                        sendRewards(leavingPlayer, false);   // Loser gets participation rewards

                        log.info("OneVsOne match ended due to player leaving - Winner: {}, Left: {}",
                                remainingPlayer.getPlayerName(), leavingPlayer.getPlayerName());
                    }

                    // Clean up match data only (don't teleport the leaving player as they're already leaving)
                    cleanupMatchDataOnly(match);
                }
            } else {
                // Match was already cleaned up, just remove player mapping
                playerToMatch.remove(player.getObjectId());
                log.debug("Player {} left instance but match was already cleaned up", player.getName());
            }
        }
    }

    /**
     * Handle player revival in OneVsOne match
     */
    public boolean handlePlayerRevive(Player player) {
        Integer matchInstanceId = playerToMatch.get(player.getObjectId());
        if (matchInstanceId == null) {
            return false; // Not in a match
        }

        OneVsOneMatch match = activeMatches.get(matchInstanceId);
        if (match == null) {
            return false; // Match not found
        }

        // Don't allow manual revival during active rounds
        if (match.getState() == OneVsOneMatch.MatchState.FIGHTING) {
            return false; // Let the round system handle revival
        }

        // Allow revival during preparation or countdown
        PlayerReviveService.revive(player, 100, 100, true, 0);
        player.getGameStats().updateStatsAndSpeedVisually();
        return true;
    }

    /**
     * Check if player can enter (2 entries per hour limit)
     */
    private boolean checkEntryLimit(int playerId) {
        List<Long> entries = playerEntryHistory.get(playerId);
        if (entries == null) {
            return true; // No previous entries
        }

        long currentTime = System.currentTimeMillis();
        long oneHourAgo = currentTime - (60 * 60 * 1000); // 1 hour in milliseconds

        // Remove entries older than 1 hour
        entries.removeIf(entryTime -> entryTime < oneHourAgo);

        // Check if player has reached the limit (2 entries per hour)
        if (entries.size() >= 2) {
            Player player = World.getInstance().getPlayer(playerId);
            if (player != null) {
                PacketSendUtility.sendMessage(player,
                    "You have reached the maximum of 2 OneVsOne entries per hour. Please try again later.",
                    ChatType.BRIGHT_YELLOW_CENTER);
            }
            return false;
        }

        return true;
    }

    /**
     * Record entry time for player
     */
    private void recordEntry(int playerId) {
        List<Long> entries = playerEntryHistory.computeIfAbsent(playerId, k -> new ArrayList<>());
        entries.add(System.currentTimeMillis());
    }

    /**
     * Start the match with initial setup and countdown
     */
    private void startMatch(OneVsOneMatch match) {
        Player player1 = World.getInstance().getPlayer(match.getPlayer1().getPlayerId());
        Player player2 = World.getInstance().getPlayer(match.getPlayer2().getPlayerId());

        if (player1 == null || player2 == null) {
            log.error("Player disconnected during match start, cancelling match");
            activeMatches.remove(match.getInstanceId());
            return;
        }

        // Set OneVsOne participant states for both players to enable same-race PvP
        setOneVsOneParticipantState(player1);
        setOneVsOneParticipantState(player2);

        // Teleport players to their spawn points
        TeleportService.teleportTo(player1, match.getMapId(), match.getInstanceId(),
                                 match.getPlayer1SpawnCoords()[0], match.getPlayer1SpawnCoords()[1],
                                 match.getPlayer1SpawnCoords()[2], (byte) match.getPlayer1SpawnCoords()[3]);

        TeleportService.teleportTo(player2, match.getMapId(), match.getInstanceId(),
                                 match.getPlayer2SpawnCoords()[0], match.getPlayer2SpawnCoords()[1],
                                 match.getPlayer2SpawnCoords()[2], (byte) match.getPlayer2SpawnCoords()[3]);

        // Announce match start
        PacketSendUtility.sendMessage(player1,
            String.format("🏟️ OneVsOne Arena! Your opponent: %s (Level %d) - Best of 3 rounds!",
                match.getPlayer2().getPlayerName(), match.getPlayer2().getPlayerLevel()),
            ChatType.BRIGHT_YELLOW_CENTER);

        PacketSendUtility.sendMessage(player2,
            String.format("🏟️ OneVsOne Arena! Your opponent: %s (Level %d) - Best of 3 rounds!",
                match.getPlayer1().getPlayerName(), match.getPlayer1().getPlayerLevel()),
            ChatType.BRIGHT_YELLOW_CENTER);

        // Schedule match timeout
        ThreadPoolManager.getInstance().schedule(() -> {
            if (activeMatches.containsKey(match.getInstanceId())) {
                log.info("OneVsOne match timed out: {} vs {}",
                        match.getPlayer1().getPlayerName(), match.getPlayer2().getPlayerName());
                handleMatchEnd(match);
            }
        }, CustomConfig.ONEVSONE_MATCH_DURATION_MINUTES * 60 * 1000);

        // Start first round after teleportation is complete
        ThreadPoolManager.getInstance().schedule(() -> {
            startNextRound(match);
        }, 2000); // 2 second delay to ensure teleportation is complete
    }

    /**
     * Start the next round with countdown
     */
    private void startNextRound(OneVsOneMatch match) {
        Player player1 = World.getInstance().getPlayer(match.getPlayer1().getPlayerId());
        Player player2 = World.getInstance().getPlayer(match.getPlayer2().getPlayerId());

        if (player1 == null || player2 == null) {
            log.warn("Player disconnected during round start, ending match");
            handleMatchEnd(match);
            return;
        }

        // Send round announcement
        String roundMessage = "🌀 " + match.getRoundAnnouncement();
        PacketSendUtility.sendMessage(player1, roundMessage, ChatType.BRIGHT_YELLOW_CENTER);
        PacketSendUtility.sendMessage(player2, roundMessage, ChatType.BRIGHT_YELLOW_CENTER);

        // Teleport players back to their original spawn points for the new round
        float[] coords1 = match.getPlayer1SpawnCoords();
        float[] coords2 = match.getPlayer2SpawnCoords();

        log.debug("Teleporting players back to spawn points for round {}: Player1 to ({}, {}, {}), Player2 to ({}, {}, {})",
                match.getCurrentRound(), coords1[0], coords1[1], coords1[2], coords2[0], coords2[1], coords2[2]);

        // Send teleportation message
        PacketSendUtility.sendMessage(player1, "Teleporting to starting position...", ChatType.BRIGHT_YELLOW_CENTER);
        PacketSendUtility.sendMessage(player2, "Teleporting to starting position...", ChatType.BRIGHT_YELLOW_CENTER);

        // Use teleportTo with animation for better visibility
        TeleportService.teleportTo(player1, match.getMapId(), match.getInstanceId(),
                                 coords1[0], coords1[1], coords1[2], (byte) coords1[3], TeleportAnimation.FADE_OUT_BEAM);
        TeleportService.teleportTo(player2, match.getMapId(), match.getInstanceId(),
                                 coords2[0], coords2[1], coords2[2], (byte) coords2[3], TeleportAnimation.FADE_OUT_BEAM);

        // Wait longer for teleportation to complete, then start countdown with immobilization
        ThreadPoolManager.getInstance().schedule(() -> {
            // Verify players are at correct positions
            log.debug("Starting countdown for round {} - Player1 at ({}, {}, {}), Player2 at ({}, {}, {})",
                    match.getCurrentRound(), player1.getX(), player1.getY(), player1.getZ(),
                    player2.getX(), player2.getY(), player2.getZ());
            startCountdown(match, player1, player2);
        }, 2000); // 2 second delay to ensure teleportation is complete
    }

    /**
     * Start 20-second countdown before round begins
     */
    private void startCountdown(OneVsOneMatch match, Player player1, Player player2) {
        // Validate players before starting countdown
        if (player1 == null || player2 == null) {
            log.warn("Cannot start countdown - one or both players are null");
            cleanupMatchWithoutWinner(match);
            return;
        }

        if (!player1.isOnline() || !player2.isOnline()) {
            log.warn("Cannot start countdown - one or both players are offline");
            cleanupMatchWithoutWinner(match);
            return;
        }

        match.setState(OneVsOneMatch.MatchState.COUNTDOWN);
        match.setCountdownActive(true);

        log.debug("Starting countdown for round {} - Player1: {}, Player2: {}",
                match.getCurrentRound(), player1.getName(), player2.getName());

        // Notify players they are frozen during countdown
        PacketSendUtility.sendMessage(player1, "🔒 You are frozen in place during countdown!", ChatType.BRIGHT_YELLOW_CENTER);
        PacketSendUtility.sendMessage(player2, "🔒 You are frozen in place during countdown!", ChatType.BRIGHT_YELLOW_CENTER);

        // Apply immobilization effect to both players for 20 seconds
        // Wait longer to ensure players are fully teleported and loaded in the PvP world
        ThreadPoolManager.getInstance().schedule(() -> {
            // Double-check players are still valid and in the correct world
            if (player1 == null || player2 == null || !player1.isOnline() || !player2.isOnline()) {
                log.warn("Players became invalid during countdown setup");
                return;
            }

            // Verify players are in the match instance
            if (player1.getWorldId() != match.getMapId() || player2.getWorldId() != match.getMapId()) {
                log.warn("Players not yet in match instance, delaying root application");
                // Try again after another second
                ThreadPoolManager.getInstance().schedule(() -> {
                    applyRootEffectToPlayers(player1, player2, match);
                }, 1000);
                return;
            }

            applyRootEffectToPlayers(player1, player2, match);
        }, 1000); // Longer delay to ensure players are fully loaded in the PvP world

        // Countdown from 20 to 1
        for (int i = 20; i >= 1; i--) {
            final int countdown = i;
            ThreadPoolManager.getInstance().schedule(() -> {
                if (match.isCountdownActive()) {
                    String message = "⏰ " + countdown;
                    PacketSendUtility.sendMessage(player1, message, ChatType.BRIGHT_YELLOW_CENTER);
                    PacketSendUtility.sendMessage(player2, message, ChatType.BRIGHT_YELLOW_CENTER);

                    // Check for stuck death states during countdown (every 5 seconds)
                    if (countdown % 5 == 0) {
                        checkAndFixDeathStatesDuringMatch(player1, player2, match);
                    }
                }
            }, (20 - i) * 1000 + 500); // Add 500ms offset to account for immobilization delay
        }

        // Start fighting after countdown
        ThreadPoolManager.getInstance().schedule(() -> {
            if (match.isCountdownActive() && player1 != null && player2 != null && player1.isOnline() && player2.isOnline()) {
                match.setCountdownActive(false);
                match.setState(OneVsOneMatch.MatchState.FIGHTING);
                match.setRoundStartTime(System.currentTimeMillis());

                try {
                    // Remove only the root effects, preserve candy transformations
                    removeOnlyRootEffects(player1);
                    removeOnlyRootEffects(player2);

                    log.debug("Removed immobilization effects for round {} - FIGHT begins!", match.getCurrentRound());
                } catch (Exception e) {
                    log.error("Error removing immobilization effects: {}", e.getMessage());
                }

                PacketSendUtility.sendMessage(player1, "⚔️ FIGHT!", ChatType.BRIGHT_YELLOW_CENTER);
                PacketSendUtility.sendMessage(player2, "⚔️ FIGHT!", ChatType.BRIGHT_YELLOW_CENTER);

                // Start periodic death state monitoring during fighting (every 10 seconds)
                startFightingPhaseMonitoring(match, player1, player2);
            } else {
                log.warn("Countdown ended but match state is invalid - cleaning up");
                cleanupMatchWithoutWinner(match);
            }
        }, 20500); // 20.5 seconds to account for immobilization delay
    }

    /**
     * Clean up match when there's no clear winner (e.g., player disconnect)
     */
    private void cleanupMatchWithoutWinner(OneVsOneMatch match) {
        // Get both participants
        OneVsOneParticipant participant1 = match.getPlayer1();
        OneVsOneParticipant participant2 = match.getPlayer2();

        // Try to get players
        Player player1 = participant1 != null ? World.getInstance().getPlayer(participant1.getPlayerId()) : null;
        Player player2 = participant2 != null ? World.getInstance().getPlayer(participant2.getPlayerId()) : null;

        // Clean up player states and teleport back
        if (player1 != null) {
            resurrectPlayerForTeleportation(player1);
            unsetOneVsOneParticipantState(player1);
            // First teleportation
            teleportPlayerToPortalLocation(player1);
            PacketSendUtility.sendMessage(player1,
                "Match ended due to technical issues. You have been teleported back to the portal.",
                ChatType.BRIGHT_YELLOW_CENTER);

            // Second teleportation after delay to clear any remaining dialogs
            ThreadPoolManager.getInstance().schedule(() -> {
                if (player1.isOnline()) {
                    teleportPlayerToPortalLocation(player1);
                    log.debug("Second teleportation completed for player1 {}", player1.getName());
                }
            }, 1500);
        }

        if (player2 != null) {
            resurrectPlayerForTeleportation(player2);
            unsetOneVsOneParticipantState(player2);
            // First teleportation
            teleportPlayerToPortalLocation(player2);
            PacketSendUtility.sendMessage(player2,
                "Match ended due to technical issues. You have been teleported back to the portal.",
                ChatType.BRIGHT_YELLOW_CENTER);

            // Second teleportation after delay to clear any remaining dialogs
            ThreadPoolManager.getInstance().schedule(() -> {
                if (player2.isOnline()) {
                    teleportPlayerToPortalLocation(player2);
                    log.debug("Second teleportation completed for player2 {}", player2.getName());
                }
            }, 1500);
        }

        // Clean up match data
        if (participant1 != null && participant2 != null) {
            cleanupMatch(participant1, participant2);
        }

        // Remove from active matches
        activeMatches.remove(match.getInstanceId());
        if (participant1 != null) {
            playerToMatch.remove(participant1.getPlayerId());
        }
        if (participant2 != null) {
            playerToMatch.remove(participant2.getPlayerId());
        }

        log.info("OneVsOne match {} cleaned up without winner", match.getInstanceId());
    }

    /**
     * Clean up match data only without teleporting players (used when players are already leaving)
     */
    private void cleanupMatchDataOnly(OneVsOneMatch match) {
        // Get both participants
        OneVsOneParticipant participant1 = match.getPlayer1();
        OneVsOneParticipant participant2 = match.getPlayer2();

        // Try to get players and clean up their states only
        Player player1 = participant1 != null ? World.getInstance().getPlayer(participant1.getPlayerId()) : null;
        Player player2 = participant2 != null ? World.getInstance().getPlayer(participant2.getPlayerId()) : null;

        // Clean up player states without teleporting
        if (player1 != null) {
            unsetOneVsOneParticipantState(player1);
        }

        if (player2 != null) {
            unsetOneVsOneParticipantState(player2);
        }

        // Clean up match data
        if (participant1 != null && participant2 != null) {
            cleanupMatch(participant1, participant2);
        }

        // Remove from active matches
        activeMatches.remove(match.getInstanceId());
        if (participant1 != null) {
            playerToMatch.remove(participant1.getPlayerId());
        }
        if (participant2 != null) {
            playerToMatch.remove(participant2.getPlayerId());
        }

        log.info("OneVsOne match {} cleaned up (data only)", match.getInstanceId());
    }

    /**
     * Reset player cooldowns properly (based on //removecd command logic)
     */
    private void resetPlayerCooldowns(Player player) {
        try {
            // Reset skill cooldowns
            java.util.Map<Integer, Long> skillCooldowns = player.getSkillCoolDowns();
            if (skillCooldowns != null && !skillCooldowns.isEmpty()) {
                long currentTime = System.currentTimeMillis();
                for (java.util.Map.Entry<Integer, Long> entry : skillCooldowns.entrySet()) {
                    player.setSkillCoolDown(entry.getKey(), currentTime);
                }
                // Use the stored reference to avoid null pointer issues
                PacketSendUtility.sendPacket(player, new com.aionemu.gameserver.network.aion.serverpackets.SM_SKILL_COOLDOWN(skillCooldowns));
            }

            // Reset item cooldowns
            java.util.Map<Integer, com.aionemu.gameserver.model.items.ItemCooldown> dummyCds = new java.util.HashMap<>();
            for (java.util.Map.Entry<Integer, com.aionemu.gameserver.model.items.ItemCooldown> entry : player.getItemCoolDowns().entrySet()) {
                dummyCds.put(entry.getKey(), new com.aionemu.gameserver.model.items.ItemCooldown(entry.getValue().getReuseTime(), 0));
                player.removeItemCoolDown(entry.getKey());
            }
            PacketSendUtility.sendPacket(player, new com.aionemu.gameserver.network.aion.serverpackets.SM_ITEM_COOLDOWN(dummyCds));

            // Reset house object cooldowns
            player.getHouseObjectCooldowns().clear();

            log.debug("Reset all cooldowns for player {} in OneVsOne match", player.getName());
        } catch (Exception e) {
            log.error("Error resetting cooldowns for player {}: {}", player.getName(), e.getMessage());
        }
    }

    /**
     * Remove only harmful effects while preserving candy transformations and other beneficial effects
     */
    private void removeOnlyHarmfulEffects(Player player) {
        try {
            // Remove debuffs (harmful effects) but preserve buffs and transformations
            player.getEffectController().removeByDispelSlotType(com.aionemu.gameserver.skillengine.model.DispelSlotType.DEBUFF);

            // Remove specific harmful effect types
            player.getEffectController().removeStunEffects();
            player.getEffectController().removeParalyzeEffects();

            // Remove specific root effects
            player.getEffectController().removeEffect(618);
            player.getEffectController().unsetAbnormal(com.aionemu.gameserver.skillengine.effect.AbnormalState.ROOT);

            // Note: This preserves candy transformations (which are usually BUFF slot) and other beneficial effects
            log.debug("Removed only harmful effects for player {}, preserving candy transformations and buffs", player.getName());
        } catch (Exception e) {
            log.error("Error removing harmful effects for player {}: {}", player.getName(), e.getMessage());
            // Fallback: if the selective removal fails, just remove root effects
            removeOnlyRootEffects(player);
        }
    }

    /**
     * Remove only root effects while preserving candy transformations and other beneficial effects
     */
    private void removeOnlyRootEffects(Player player) {
        try {
            // Remove specific root effect (skill 618)
            player.getEffectController().removeEffect(618);

            // Unset root abnormal state
            player.getEffectController().unsetAbnormal(com.aionemu.gameserver.skillengine.effect.AbnormalState.ROOT);

            // Abort any current movement to ensure they can move
            player.getMoveController().abortMove();

            log.debug("Removed only root effects for player {}, preserving other effects", player.getName());
        } catch (Exception e) {
            log.error("Error removing root effects for player {}: {}", player.getName(), e.getMessage());
        }
    }

    /**
     * Apply unremovable root effect to both players during countdown
     */
    private void applyRootEffectToPlayers(Player player1, Player player2, OneVsOneMatch match) {
        try {
            log.debug("Attempting to apply unremovable root effects to players {} and {} in round {}",
                     player1.getName(), player2.getName(), match.getCurrentRound());

            // Apply initial root effect
            applyRootToPlayer(player1);
            applyRootToPlayer(player2);

            // Set up continuous root enforcement during countdown (every 2 seconds)
            for (int i = 0; i < 10; i++) { // 20 seconds / 2 = 10 iterations
                final int iteration = i;
                ThreadPoolManager.getInstance().schedule(() -> {
                    if (match.isCountdownActive() && match.getState() == OneVsOneMatch.MatchState.COUNTDOWN) {
                        // Continuously reapply root to prevent removal by potions/skills
                        applyRootToPlayer(player1);
                        applyRootToPlayer(player2);

                        log.debug("Root enforcement iteration {} - Reapplied root to both players", iteration + 1);
                    }
                }, (i + 1) * 2000); // Every 2 seconds
            }

            log.debug("Applied unremovable root effects (skill 618) to both players in OneVsOne round {} with continuous enforcement", match.getCurrentRound());
        } catch (Exception e) {
            log.error("Error applying root effects to players: {}", e.getMessage(), e);
        }
    }

    /**
     * Apply root effect to a single player with multiple methods for reliability
     */
    private void applyRootToPlayer(Player player) {
        try {
            // Method 1: Apply root effect (skill 618) with ForceType.DEFAULT
            com.aionemu.gameserver.skillengine.model.Effect.ForceType forceType = com.aionemu.gameserver.skillengine.model.Effect.ForceType.DEFAULT;
            com.aionemu.gameserver.skillengine.SkillEngine.getInstance()
                .applyEffectDirectly(618, player, player, 25000, forceType); // 25 second root (longer than countdown)

            // Method 2: Directly set abnormal state as backup
            player.getEffectController().setAbnormal(com.aionemu.gameserver.skillengine.effect.AbnormalState.ROOT);

            // Method 3: Abort any current movement
            player.getMoveController().abortMove();

            // Method 4: Disable skill usage during countdown by setting a temporary state
            // This prevents players from using skills to remove the root
            player.setSkillCoolDown(0, System.currentTimeMillis() + 25000); // Global cooldown

        } catch (Exception e) {
            log.error("Error applying root to player {}: {}", player.getName(), e.getMessage());
        }
    }

    /**
     * Start monitoring death states during fighting phase
     */
    private void startFightingPhaseMonitoring(OneVsOneMatch match, Player player1, Player player2) {
        // Schedule periodic checks every 10 seconds during fighting
        for (int i = 1; i <= 30; i++) { // Monitor for up to 5 minutes (30 * 10 seconds)
            final int checkNumber = i;
            ThreadPoolManager.getInstance().schedule(() -> {
                // Only check if match is still in fighting state
                if (match.getState() == OneVsOneMatch.MatchState.FIGHTING) {
                    checkForStuckDeathStatesDuringFighting(player1, player2, match, checkNumber);
                }
            }, i * 10000); // Every 10 seconds
        }
    }

    /**
     * Check for stuck death states during fighting phase
     */
    private void checkForStuckDeathStatesDuringFighting(Player player1, Player player2, OneVsOneMatch match, int checkNumber) {
        try {
            boolean player1Dead = player1 != null && player1.isOnline() && player1.isDead();
            boolean player2Dead = player2 != null && player2.isOnline() && player2.isDead();

            // If a player has been dead for more than 15 seconds during fighting, it might be stuck
            // We'll track this and fix if needed
            if (player1Dead) {
                Long deathTime = playerDeathTimes.get(player1.getObjectId());
                if (deathTime == null) {
                    playerDeathTimes.put(player1.getObjectId(), System.currentTimeMillis());
                } else {
                    long deadDuration = System.currentTimeMillis() - deathTime;
                    if (deadDuration >= 1000) { // 1 second - immediate death detection
                        log.warn("OneVsOne: Player {} stuck in death for {}ms during fighting, treating as legitimate death",
                                player1.getName(), deadDuration);

                        // Award round to opponent if they're alive
                        if (player2 != null && player2.isOnline() && !player2.isDead()) {
                            log.info("OneVsOne: Awarding round to {} due to {} being stuck in death state",
                                    player2.getName(), player1.getName());
                            onPlayerKill(player2, player1);
                        }
                        playerDeathTimes.remove(player1.getObjectId());
                    }
                }
            } else {
                playerDeathTimes.remove(player1.getObjectId());
            }

            // Same check for player2
            if (player2Dead) {
                Long deathTime = playerDeathTimes.get(player2.getObjectId());
                if (deathTime == null) {
                    playerDeathTimes.put(player2.getObjectId(), System.currentTimeMillis());
                } else {
                    long deadDuration = System.currentTimeMillis() - deathTime;
                    if (deadDuration >= 1000) { // 1 second - immediate death detection
                        log.warn("OneVsOne: Player {} stuck in death for {}ms during fighting, treating as legitimate death",
                                player2.getName(), deadDuration);

                        // Award round to opponent if they're alive
                        if (player1 != null && player1.isOnline() && !player1.isDead()) {
                            log.info("OneVsOne: Awarding round to {} due to {} being stuck in death",
                                    player1.getName(), player2.getName());
                            onPlayerKill(player1, player2);
                        }
                        playerDeathTimes.remove(player2.getObjectId());
                    }
                }
            } else {
                playerDeathTimes.remove(player2.getObjectId());
            }

        } catch (Exception e) {
            log.error("Error checking stuck death states during fighting: {}", e.getMessage(), e);
        }
    }

    /**
     * Check and fix death states during active match (called during countdown and fighting)
     */
    private void checkAndFixDeathStatesDuringMatch(Player player1, Player player2, OneVsOneMatch match) {
        try {
            // Check player1
            if (player1 != null && player1.isOnline() && player1.isDead()) {
                // During countdown, players should not be dead
                if (match.getState() == OneVsOneMatch.MatchState.COUNTDOWN) {
                    log.warn("OneVsOne: Player {} is dead during countdown, auto-fixing", player1.getName());
                    forceResurrectForNextRound(player1, match);
                    PacketSendUtility.sendMessage(player1,
                        "You were stuck in death state and have been automatically resurrected.",
                        ChatType.BRIGHT_YELLOW_CENTER);
                }
            }

            // Check player2
            if (player2 != null && player2.isOnline() && player2.isDead()) {
                // During countdown, players should not be dead
                if (match.getState() == OneVsOneMatch.MatchState.COUNTDOWN) {
                    log.warn("OneVsOne: Player {} is dead during countdown, auto-fixing", player2.getName());
                    forceResurrectForNextRound(player2, match);
                    PacketSendUtility.sendMessage(player2,
                        "You were stuck in death state and have been automatically resurrected.",
                        ChatType.BRIGHT_YELLOW_CENTER);
                }
            }
        } catch (Exception e) {
            log.error("Error checking death states during match: {}", e.getMessage(), e);
        }
    }

    /**
     * Monitor players for stuck death states and automatically fix them
     */
    private void monitorDeathStates() {
        if (!systemActive || activePlayers.isEmpty()) {
            return;
        }

        long currentTime = System.currentTimeMillis();
        final long DEATH_TIMEOUT_MS = 1000; // 1 second - trigger immediate revival in OneVsOne

        try {
            // Check all active players for death state issues
            for (OneVsOneParticipant participant : activePlayers.values()) {
                Player player = World.getInstance().getPlayer(participant.getPlayerId());
                if (player == null || !player.isOnline()) {
                    // Clean up death tracking for offline players
                    playerDeathTimes.remove(participant.getPlayerId());
                    continue;
                }

                // Check if player is dead
                if (player.isDead()) {
                    Long deathTime = playerDeathTimes.get(participant.getPlayerId());

                    if (deathTime == null) {
                        // First time we detect this player is dead, record the time
                        playerDeathTimes.put(participant.getPlayerId(), currentTime);
                        log.debug("OneVsOne: Detected player {} is dead, starting death timer", player.getName());
                    } else {
                        // Check if player has been dead too long
                        long deadDuration = currentTime - deathTime;
                        if (deadDuration >= DEATH_TIMEOUT_MS) {
                            log.warn("OneVsOne: Player {} has been stuck in death state for {}ms, auto-fixing",
                                    player.getName(), deadDuration);

                            // Auto-fix the stuck death state
                            autoFixStuckDeathState(player, participant);

                            // Remove from death tracking
                            playerDeathTimes.remove(participant.getPlayerId());
                        }
                    }
                } else {
                    // Player is alive, remove from death tracking if present
                    if (playerDeathTimes.remove(participant.getPlayerId()) != null) {
                        log.debug("OneVsOne: Player {} is no longer dead, removed from death tracking", player.getName());
                    }
                }
            }
        } catch (Exception e) {
            log.error("Error monitoring death states in OneVsOne: {}", e.getMessage(), e);
        }
    }

    /**
     * Automatically fix a player stuck in death state
     */
    private void autoFixStuckDeathState(Player player, OneVsOneParticipant participant) {
        try {
            log.info("OneVsOne: Auto-fixing stuck death state for player {}", player.getName());

            // Get the match this player is in
            Integer matchInstanceId = playerToMatch.get(participant.getPlayerId());
            OneVsOneMatch match = matchInstanceId != null ? activeMatches.get(matchInstanceId) : null;

            if (match == null) {
                log.warn("OneVsOne: Cannot find match for stuck player {}, removing from active players", player.getName());
                // Remove from active players and teleport to portal
                activePlayers.remove(participant.getPlayerId());
                playerToMatch.remove(participant.getPlayerId());

                // Force resurrect and teleport
                forceResurrectAndTeleport(player);
                return;
            }

            // Handle based on match state
            switch (match.getState()) {
                case FIGHTING:
                    // During fighting, this might be a legitimate death that should trigger round end
                    log.info("OneVsOne: Player {} stuck in death during FIGHTING state, treating as round death", player.getName());

                    // Get opponent
                    OneVsOneParticipant opponent = match.getOpponent(participant.getPlayerId());
                    if (opponent != null) {
                        Player opponentPlayer = World.getInstance().getPlayer(opponent.getPlayerId());
                        if (opponentPlayer != null && !opponentPlayer.isDead()) {
                            // Opponent is alive, award them the round
                            log.info("OneVsOne: Awarding round to {} due to {} being stuck in death state",
                                    opponentPlayer.getName(), player.getName());
                            onPlayerKill(opponentPlayer, player);
                            return;
                        }
                    }

                    // If opponent is also dead or not found, just resurrect both
                    log.warn("OneVsOne: Both players in problematic state, force-resurrecting for next round");
                    forceResurrectForNextRound(player, match);
                    break;

                case COUNTDOWN:
                case ROUND_END:
                case PREPARING:
                    // During non-fighting states, player should not be dead
                    log.info("OneVsOne: Player {} stuck in death during {} state, force-resurrecting",
                            player.getName(), match.getState());
                    forceResurrectForNextRound(player, match);
                    break;

                case MATCH_END:
                    // Match is ending, just clean up
                    log.info("OneVsOne: Player {} stuck in death during MATCH_END, cleaning up", player.getName());
                    forceResurrectAndTeleport(player);
                    break;

                default:
                    log.warn("OneVsOne: Unknown match state {} for stuck player {}, force-resurrecting",
                            match.getState(), player.getName());
                    forceResurrectForNextRound(player, match);
                    break;
            }

        } catch (Exception e) {
            log.error("OneVsOne: Error auto-fixing stuck death state for player {}: {}", player.getName(), e.getMessage(), e);

            // Fallback: force resurrect and teleport to portal
            try {
                forceResurrectAndTeleport(player);
            } catch (Exception fallbackError) {
                log.error("OneVsOne: Fallback resurrection also failed for player {}: {}",
                         player.getName(), fallbackError.getMessage());
            }
        }
    }

    /**
     * Force resurrect player and teleport them back to portal (emergency cleanup)
     */
    private void forceResurrectAndTeleport(Player player) {
        try {
            log.info("OneVsOne: Force-resurrecting and teleporting player {} to portal", player.getName());

            // Clear any resurrection dialogs or states
            player.unsetResPosState();
            player.setPlayerResActivate(false);

            // Force resurrect with full HP/MP
            PlayerReviveService.revive(player, 100, 100, false, 0);

            // Reset cooldowns
            resetPlayerCooldowns(player);

            // Remove harmful effects
            removeOnlyHarmfulEffects(player);

            // Update stats
            if (player.getGameStats() != null) {
                player.getGameStats().updateStatsAndSpeedVisually();
            }

            // Remove OneVsOne states
            unsetOneVsOneParticipantState(player);

            // Teleport to portal
            teleportPlayerToPortalLocation(player);

            // Send message to player
            PacketSendUtility.sendMessage(player,
                "You were stuck in death state and have been automatically fixed. You've been teleported back to the OneVsOne portal.",
                ChatType.BRIGHT_YELLOW_CENTER);

            log.info("OneVsOne: Successfully force-resurrected and teleported player {} to portal", player.getName());

        } catch (Exception e) {
            log.error("OneVsOne: Error in force resurrect and teleport for player {}: {}", player.getName(), e.getMessage(), e);
        }
    }

    /**
     * Force resurrect player for next round (used when stuck during active match)
     */
    private void forceResurrectForNextRound(Player player, OneVsOneMatch match) {
        try {
            log.info("OneVsOne: Force-resurrecting player {} for next round", player.getName());

            // Clear any resurrection dialogs or states
            player.unsetResPosState();
            player.setPlayerResActivate(false);

            // Force resurrect with full HP/MP
            PlayerReviveService.revive(player, 100, 100, false, 0);

            // Reset cooldowns
            resetPlayerCooldowns(player);

            // Remove harmful effects
            removeOnlyHarmfulEffects(player);

            // Update stats
            if (player.getGameStats() != null) {
                player.getGameStats().updateStatsAndSpeedVisually();
            }

            // Send message to player
            PacketSendUtility.sendMessage(player,
                "You were stuck in death state and have been automatically resurrected.",
                ChatType.BRIGHT_YELLOW_CENTER);

            log.info("OneVsOne: Successfully force-resurrected player {} for match continuation", player.getName());

        } catch (Exception e) {
            log.error("OneVsOne: Error in force resurrect for next round for player {}: {}", player.getName(), e.getMessage(), e);
        }
    }
    
    /**
     * Resurrect player for next round with full HP/MP and reset cooldowns
     */
    private void resurrectPlayerForNextRound(Player player, OneVsOneMatch match) {
        if (player == null) {
            log.warn("Cannot resurrect null player for next round");
            return;
        }

        try {
            if (player.isDead()) {
                // Clear any resurrection dialogs or stuck states first
                player.unsetResPosState();
                player.setPlayerResActivate(false);

                // Resurrect with full HP/MP
                PlayerReviveService.revive(player, 100, 100, false, 0);

                log.debug("Resurrected dead player {} for next round", player.getName());
            } else {
                // Just restore HP/MP if not dead
                if (player.getLifeStats() != null) {
                    player.getLifeStats().setCurrentHpPercent(100);
                    player.getLifeStats().setCurrentMpPercent(100);
                }
            }

            // Reset all cooldowns properly (like //removecd command)
            resetPlayerCooldowns(player);

            // Remove only harmful effects, preserve candy transformations and beneficial effects
            if (player.getEffectController() != null) {
                removeOnlyHarmfulEffects(player);
            }

            // Update stats
            if (player.getGameStats() != null) {
                player.getGameStats().updateStatsAndSpeedVisually();
            }

            // Send resurrection message
            PacketSendUtility.sendMessage(player,
                "Resurrected for next round with full HP/MP and reset cooldowns!",
                ChatType.BRIGHT_YELLOW_CENTER);

        } catch (Exception e) {
            log.error("Error resurrecting player {} for next round: {}", player.getName(), e.getMessage());
        }

        // Note: Teleportation is now handled in startNextRound() to ensure proper timing with immobilization
    }

    /**
     * Resurrect player for match end teleportation (similar to round resurrection but for final cleanup)
     */
    private void resurrectPlayerForTeleportation(Player player) {
        if (player == null) {
            log.warn("Cannot resurrect null player for teleportation");
            return;
        }

        try {
            if (player.isDead()) {
                // Resurrect with full HP/MP without showing resurrection dialog
                PlayerReviveService.revive(player, 100, 100, false, 0);
                log.debug("Resurrected player {} for teleportation", player.getName());

                // Clear any resurrection state to prevent dialog from showing
                player.unsetResPosState();
                player.setPlayerResActivate(false);
            }

            // Reset cooldowns when match ends (like round resurrection)
            resetPlayerCooldowns(player);

            // Update stats to ensure proper state
            if (player.getGameStats() != null) {
                player.getGameStats().updateStatsAndSpeedVisually();
            }

        } catch (Exception e) {
            log.error("Error resurrecting player {} for teleportation: {}", player.getName(), e.getMessage());
        }
    }
}