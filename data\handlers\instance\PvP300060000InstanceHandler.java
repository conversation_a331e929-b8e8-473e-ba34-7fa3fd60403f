package instance;

import java.util.ArrayList;
import java.util.List;

import com.aionemu.gameserver.instance.handlers.GeneralInstanceHandler;
import com.aionemu.gameserver.instance.handlers.InstanceID;
import com.aionemu.gameserver.model.gameobjects.Creature;
import com.aionemu.gameserver.model.gameobjects.player.Player;
import com.aionemu.gameserver.services.FFAService;
import com.aionemu.gameserver.services.OneVsOneService;
import com.aionemu.gameserver.services.player.PlayerReviveService;
import com.aionemu.gameserver.utils.PacketSendUtility;
import com.aionemu.gameserver.world.WorldMapInstance;

/**
 * Unified PvP Instance Handler for Map 300060000
 * 
 * Handles both OneVsOne and FFA events in the same instance map.
 * Determines which system to use based on player participation.
 * 
 * <AUTHOR> System
 */
@InstanceID(300060000)
public class PvP300060000InstanceHandler extends GeneralInstanceHandler {

    // FFA spawn locations for this map
    private final List<float[]> ffaSpawnLocations = new ArrayList<>();

    public PvP300060000InstanceHandler(WorldMapInstance instance) {
        super(instance);
        initializeFFASpawnLocations();
    }

    private void initializeFFASpawnLocations() {
        // Add FFA spawn locations for this map
        ffaSpawnLocations.add(new float[]{1425.028f, 1277.586f, 564.1405f, 0});
        ffaSpawnLocations.add(new float[]{1400.0f, 1300.0f, 564.0f, 0});
        ffaSpawnLocations.add(new float[]{1450.0f, 1250.0f, 564.0f, 0});
        ffaSpawnLocations.add(new float[]{1375.0f, 1325.0f, 564.0f, 0});
        ffaSpawnLocations.add(new float[]{1475.0f, 1225.0f, 564.0f, 0});
        ffaSpawnLocations.add(new float[]{1350.0f, 1350.0f, 564.0f, 0});
        ffaSpawnLocations.add(new float[]{1500.0f, 1200.0f, 564.0f, 0});
        ffaSpawnLocations.add(new float[]{1325.0f, 1375.0f, 564.0f, 0});
        ffaSpawnLocations.add(new float[]{1525.0f, 1175.0f, 564.0f, 0});
        ffaSpawnLocations.add(new float[]{1300.0f, 1400.0f, 564.0f, 0});
    }

    public List<float[]> getFFASpawnLocations() {
        return ffaSpawnLocations;
    }

    @Override
    public void onEnterInstance(Player player) {
        super.onEnterInstance(player);
        // Player entered PvP instance
    }

    @Override
    public void onLeaveInstance(Player player) {
        super.onLeaveInstance(player);
        
        // Clean up player from OneVsOne system when they leave
        if (OneVsOneService.getInstance().isPlayerInActiveMatch(player)) {
            OneVsOneService.getInstance().handlePlayerLeave(player);
        }
        
        // FFA cleanup is handled by FFAService when players leave the event
    }

    @Override
    public boolean onDie(Player player, Creature lastAttacker) {
        System.out.println("DEBUG: PvP300060000 onDie called for player: " + player.getName());
        System.out.println("DEBUG: lastAttacker: " + (lastAttacker != null ? lastAttacker.getName() : "null"));
        System.out.println("DEBUG: lastAttacker instanceof Player: " + (lastAttacker instanceof Player));

        // Check FFA first (higher priority for multi-player events)
        boolean isFFAParticipant = FFAService.getInstance().isParticipant(player);
        System.out.println("DEBUG: isFFAParticipant for " + player.getName() + ": " + isFFAParticipant);

        if (isFFAParticipant) {
            System.out.println("FFA Death: " + player.getName() + " killed by " +
                (lastAttacker instanceof Player ? ((Player) lastAttacker).getName() : "unknown"));

            // Handle kill tracking first
            if (lastAttacker instanceof Player) {
                System.out.println("DEBUG: Calling FFAService.onPlayerKill for killer: " + ((Player) lastAttacker).getName() + ", victim: " + player.getName());
                FFAService.getInstance().onPlayerKill((Player) lastAttacker, player);
            } else {
                System.out.println("DEBUG: Not calling onPlayerKill - lastAttacker is not a Player");
            }

            // FFA handles its own resurrection logic
            return true; // Prevent default death handling and resurrection dialog
        }
        
        // Check OneVsOne second
        if (OneVsOneService.getInstance().isPlayerInActiveMatch(player)) {
            // Always immediately resurrect players in OneVsOne matches to prevent resurrection dialog
            PlayerReviveService.revive(player, 100, 100, false, 0);
            player.getGameStats().updateStatsAndSpeedVisually();
            player.unsetResPosState();
            player.setPlayerResActivate(false);

            // Handle player death in OneVsOne match after resurrection
            if (lastAttacker instanceof Player) {
                OneVsOneService.getInstance().onPlayerKill((Player) lastAttacker, player);
            }
            return true; // Prevent default death handling and resurrection dialog
        }

        // Default death handling for non-PvP situations
        return super.onDie(player, lastAttacker);
    }

    @Override
    public void onInstanceDestroy() {
        // Clean up any remaining tasks or resources
        super.onInstanceDestroy();
    }
}
