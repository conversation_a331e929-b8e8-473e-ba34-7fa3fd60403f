package com.aionemu.gameserver.model.onevsone;

import com.aionemu.gameserver.model.gameobjects.player.Player;

/**
 * OneVsOne Participant Model
 * 
 * Represents a player participating in the OneVsOne PvP system.
 * Tracks player information, queue status, match statistics, and timestamps.
 * 
 * <AUTHOR> System
 */
public class OneVsOneParticipant {
    
    private final int playerId;
    private final String playerName;
    private final int playerLevel;
    private final String playerClass;
    private final long queueJoinTime;

    // Store original registration location for teleporting back after match
    private final int registrationWorldId;
    private final float registrationX;
    private final float registrationY;
    private final float registrationZ;
    private final byte registrationHeading;
    
    // Match statistics
    private int wins;
    private int losses;
    private int totalMatches;
    private long lastMatchTime;
    
    // Current match data
    private OneVsOneParticipant currentOpponent;
    private int currentMatchMapId;
    private int currentMatchInstanceId;
    private long currentMatchStartTime;
    private boolean inMatch;
    
    /**
     * Constructor for new participant
     */
    public OneVsOneParticipant(Player player) {
        this.playerId = player.getObjectId();
        this.playerName = player.getName();
        this.playerLevel = player.getLevel();
        this.playerClass = player.getPlayerClass().toString();
        this.queueJoinTime = System.currentTimeMillis();

        // Store current location as registration location
        this.registrationWorldId = player.getWorldId();
        this.registrationX = player.getX();
        this.registrationY = player.getY();
        this.registrationZ = player.getZ();
        this.registrationHeading = player.getHeading();

        // Initialize statistics
        this.wins = 0;
        this.losses = 0;
        this.totalMatches = 0;
        this.lastMatchTime = 0;
        
        // Initialize match data
        this.currentOpponent = null;
        this.currentMatchMapId = 0;
        this.currentMatchInstanceId = 0;
        this.currentMatchStartTime = 0;
        this.inMatch = false;
    }
    
    // Basic getters
    public int getPlayerId() { return playerId; }
    public String getPlayerName() { return playerName; }
    public int getPlayerLevel() { return playerLevel; }
    public String getPlayerClass() { return playerClass; }
    public long getQueueJoinTime() { return queueJoinTime; }

    // Registration location getters
    public int getRegistrationWorldId() { return registrationWorldId; }
    public float getRegistrationX() { return registrationX; }
    public float getRegistrationY() { return registrationY; }
    public float getRegistrationZ() { return registrationZ; }
    public byte getRegistrationHeading() { return registrationHeading; }
    
    // Statistics getters
    public int getWins() { return wins; }
    public int getLosses() { return losses; }
    public int getTotalMatches() { return totalMatches; }
    public long getLastMatchTime() { return lastMatchTime; }
    
    // Match data getters
    public OneVsOneParticipant getCurrentOpponent() { return currentOpponent; }
    public int getCurrentMatchMapId() { return currentMatchMapId; }
    public int getCurrentMatchInstanceId() { return currentMatchInstanceId; }
    public long getCurrentMatchStartTime() { return currentMatchStartTime; }
    public boolean isInMatch() { return inMatch; }
    
    // Statistics management
    public void addWin() {
        this.wins++;
        this.totalMatches++;
        this.lastMatchTime = System.currentTimeMillis();
    }
    
    public void addLoss() {
        this.losses++;
        this.totalMatches++;
        this.lastMatchTime = System.currentTimeMillis();
    }
    
    /**
     * Calculate win rate as percentage
     */
    public double getWinRate() {
        if (totalMatches == 0) {
            return 0.0;
        }
        return (double) wins / totalMatches * 100.0;
    }
    
    /**
     * Get time spent in queue in milliseconds
     */
    public long getQueueTime() {
        return System.currentTimeMillis() - queueJoinTime;
    }
    
    /**
     * Get current match duration in milliseconds
     */
    public long getCurrentMatchDuration() {
        if (!inMatch || currentMatchStartTime == 0) {
            return 0;
        }
        return System.currentTimeMillis() - currentMatchStartTime;
    }
    
    /**
     * Check if this participant can be matched with another participant
     */
    public boolean canMatchWith(OneVsOneParticipant other) {
        if (other == null || other.equals(this)) {
            return false;
        }
        
        // Check if both are not already in a match
        if (this.inMatch || other.inMatch) {
            return false;
        }
        
        // Check level difference (using config value)
        int levelDiff = Math.abs(this.playerLevel - other.playerLevel);
        return levelDiff <= com.aionemu.gameserver.configs.main.CustomConfig.ONEVSONE_MAX_LEVEL_DIFF;
    }
    
    /**
     * Reset match data after match ends
     */
    public void resetMatchData() {
        this.currentOpponent = null;
        this.currentMatchMapId = 0;
        this.currentMatchInstanceId = 0;
        this.currentMatchStartTime = 0;
        this.inMatch = false;
    }
    
    /**
     * Start a new match with opponent
     */
    public void startMatch(OneVsOneParticipant opponent, int mapId, int instanceId) {
        this.currentOpponent = opponent;
        this.currentMatchMapId = mapId;
        this.currentMatchInstanceId = instanceId;
        this.currentMatchStartTime = System.currentTimeMillis();
        this.inMatch = true;
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        OneVsOneParticipant that = (OneVsOneParticipant) obj;
        return playerId == that.playerId;
    }
    
    @Override
    public int hashCode() {
        return Integer.hashCode(playerId);
    }
    
    @Override
    public String toString() {
        return String.format("OneVsOneParticipant{name='%s', level=%d, class='%s', wins=%d, losses=%d, inMatch=%s}", 
            playerName, playerLevel, playerClass, wins, losses, inMatch);
    }
}
